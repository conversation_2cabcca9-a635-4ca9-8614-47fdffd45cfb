<?xml version="1.0" encoding="utf-8"?>
<!-- Launch background exactly matching the attached Flutter SplashScreen image -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Background gradient matching HM logo colors -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="#E91E63"
                android:centerColor="#9C27B0"
                android:endColor="#2196F3"
                android:type="linear" />
        </shape>
    </item>

    <!-- White circular logo background with shadow effect -->
    <item
        android:width="140dp"
        android:height="140dp"
        android:gravity="center"
        android:top="120dp">
        <shape android:shape="oval">
            <solid android:color="#FFFFFF" />
        </shape>
    </item>

    <!-- App logo icon positioned exactly like in the image -->
    <item
        android:width="100dp"
        android:height="100dp"
        android:gravity="center"
        android:top="140dp"
        android:drawable="@drawable/app_logo" />
</layer-list>

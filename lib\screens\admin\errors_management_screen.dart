import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/device_error_model.dart';
import '../../providers/error_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/manufacturer_provider.dart';
import '../../services/excel_service.dart';
import '../../services/backup_service.dart';
import '../../widgets/gradient_background.dart';

import '../../utils/theme.dart';
import '../errors/error_details_screen.dart';
import 'error_edit_screen.dart';

class ErrorsManagementScreen extends StatefulWidget {
  final DeviceError? errorToEdit;

  const ErrorsManagementScreen({super.key, this.errorToEdit});

  @override
  State<ErrorsManagementScreen> createState() => _ErrorsManagementScreenState();
}

class _ErrorsManagementScreenState extends State<ErrorsManagementScreen> {
  String? _selectedCategoryId;
  bool _isSelectionMode = false;
  final Set<String> _selectedErrorIds = {};

  @override
  void initState() {
    super.initState();
    // Fetch categories and manufacturers when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CategoryProvider>(context, listen: false).fetchCategories();
      Provider.of<ManufacturerProvider>(context, listen: false).fetchManufacturers();

      // If editing an error, set the category and load errors
      if (widget.errorToEdit != null) {
        _selectedCategoryId = widget.errorToEdit!.categoryId;
        Provider.of<ErrorProvider>(context, listen: false)
            .fetchErrorsByCategory(widget.errorToEdit!.categoryId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final errorProvider = Provider.of<ErrorProvider>(context);
    final categoryProvider = Provider.of<CategoryProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return GradientScaffold(
      appBar: AppBar(
        elevation: 4,
        backgroundColor: _isSelectionMode
          ? Colors.orange.shade600
          : const Color(0xFF2563EB), // خلفية زرقاء
        iconTheme: const IconThemeData(color: Colors.white), // أيقونات بيضاء
        centerTitle: true,
        title: _isSelectionMode
          ? Text(
              localeProvider.isRTL
                ? 'تم تحديد ${_selectedErrorIds.length} عنصر'
                : '${_selectedErrorIds.length} selected',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            )
          : Text(
              localeProvider.isRTL ? 'إدارة الأعطال' : 'Error Management',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
        leading: _isSelectionMode
          ? IconButton(
              icon: const Icon(Icons.close),
              onPressed: _exitSelectionMode,
              tooltip: localeProvider.isRTL ? 'إلغاء التحديد' : 'Cancel Selection',
            )
          : null,
        actions: _isSelectionMode
          ? [
              if (_selectedErrorIds.isNotEmpty) ...[
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _deleteSelectedErrors,
                  tooltip: localeProvider.isRTL ? 'حذف المحدد' : 'Delete Selected',
                ),
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: _shareSelectedErrors,
                  tooltip: localeProvider.isRTL ? 'مشاركة المحدد' : 'Share Selected',
                ),
              ],
              IconButton(
                icon: Icon(_selectedErrorIds.length == errorProvider.errors.length
                  ? Icons.deselect
                  : Icons.select_all),
                onPressed: _toggleSelectAll,
                tooltip: localeProvider.isRTL
                  ? (_selectedErrorIds.length == errorProvider.errors.length
                    ? 'إلغاء تحديد الكل'
                    : 'تحديد الكل')
                  : (_selectedErrorIds.length == errorProvider.errors.length
                    ? 'Deselect All'
                    : 'Select All'),
              ),
            ]
          : [
              IconButton(
                icon: const Icon(Icons.checklist),
                onPressed: _enterSelectionMode,
                tooltip: localeProvider.isRTL ? 'تحديد متعدد' : 'Multi Select',
              ),
              IconButton(
                icon: const Icon(Icons.refresh_rounded),
                onPressed: () {
                  if (_selectedCategoryId != null) {
                    errorProvider.fetchErrorsByCategory(_selectedCategoryId!);
                  }
                },
                tooltip: localeProvider.isRTL ? 'تحديث' : 'Refresh',
              ),
              IconButton(
                icon: const Icon(Icons.analytics_outlined),
                onPressed: () => _showStatsDialog(context),
                tooltip: localeProvider.isRTL ? 'الإحصائيات' : 'Statistics',
              ),
            ],
      ),
      body: Column(
        children: [
          // Header section with category selector
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localeProvider.isRTL ? 'اختر فئة الجهاز' : 'Select Device Category',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 12),
                  DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: localeProvider.isRTL ? 'اختر الفئة' : 'Select Category',
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        prefixIcon: Icon(
                          Icons.category_outlined,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      value: _selectedCategoryId,
                      items: categoryProvider.categories.map((category) {
                        return DropdownMenuItem<String>(
                          value: category.id,
                          child: Text(
                            category.name,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCategoryId = value;
                        });
                        if (value != null) {
                          errorProvider.fetchErrorsByCategory(value);
                        }
                      },
                    ),
                  if (_selectedCategoryId != null) ...[
                    const SizedBox(height: 16),
                    _buildStatsRow(context, errorProvider, localeProvider),
                  ],
                ],
              ),
            ),
          ),

          // Error list
          Expanded(
            child: _selectedCategoryId == null
                ? _buildEmptyState(localeProvider, 'select_category')
                : errorProvider.isLoading
                    ? const Center(
                        child: CircularProgressIndicator(strokeWidth: 3),
                      )
                    : errorProvider.errors.isEmpty
                        ? _buildEmptyState(localeProvider, 'no_errors')
                        : Container(
                            padding: const EdgeInsets.all(16),
                            child: ListView.builder(
                              itemCount: errorProvider.errors.length,
                              itemBuilder: (context, index) {
                                final error = errorProvider.errors[index];
                                return _buildErrorCard(context, error, localeProvider);
                              },
                            ),
                          ),
          ),
        ],
      ),
      floatingActionButton: _selectedCategoryId != null
          ? _buildFloatingActionMenu(context, localeProvider)
          : null,
    );
  }

  Widget _buildFloatingActionMenu(BuildContext context, LocaleProvider localeProvider) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Advanced actions button
        FloatingActionButton(
          heroTag: "bulk_actions",
          onPressed: () => _showBulkActionsDialog(context),
          tooltip: localeProvider.isRTL ? 'إجراءات متقدمة' : 'Advanced Actions',
          backgroundColor: Colors.blue.shade600,
          foregroundColor: Colors.white,
          elevation: 4,
          child: const Icon(Icons.settings, size: 24),
        ),
        const SizedBox(height: 12),
        // Add error button (primary)
        FloatingActionButton.extended(
          heroTag: "add_error",
          onPressed: () => _showAddErrorDialog(context),
          tooltip: localeProvider.isRTL ? 'إضافة عطل جديد' : 'Add New Error',
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
          elevation: 6,
          icon: const Icon(Icons.add_rounded, size: 24),
          label: Text(
            localeProvider.isRTL ? 'إضافة عطل' : 'Add Error',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }

  void _showBulkActionsDialog(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localeProvider.isRTL ? 'إجراءات متقدمة' : 'Advanced Actions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.file_download),
              title: Text(localeProvider.isRTL ? 'تصدير إلى Excel' : 'Export to Excel'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.file_upload),
              title: Text(localeProvider.isRTL ? 'استيراد من Excel' : 'Import from Excel'),
              onTap: () {
                Navigator.pop(context);
                _importFromExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.backup),
              title: Text(localeProvider.isRTL ? 'نسخ احتياطي' : 'Backup Database'),
              onTap: () {
                Navigator.pop(context);
                _backupDatabase();
              },
            ),
            ListTile(
              leading: const Icon(Icons.restore),
              title: Text(localeProvider.isRTL ? 'استعادة النسخة الاحتياطية' : 'Restore Database'),
              onTap: () {
                Navigator.pop(context);
                _restoreDatabase();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete_sweep),
              title: Text(
                localeProvider.isRTL ? 'حذف جميع الأعطال' : 'Delete All Errors',
                style: const TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                _showDeleteAllConfirmation();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAllConfirmation() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localeProvider.isRTL ? 'تأكيد الحذف' : 'Confirm Delete'),
        content: Text(
          localeProvider.isRTL
            ? 'هل أنت متأكد من حذف جميع الأعطال في هذه الفئة؟ هذا الإجراء لا يمكن التراجع عنه.'
            : 'Are you sure you want to delete all errors in this category? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAllErrors();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(localeProvider.isRTL ? 'حذف الكل' : 'Delete All'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(isRTL ? 'جاري التصدير...' : 'Exporting...'),
            ],
          ),
        ),
      );

      final excelService = ExcelService();
      final errors = _selectedCategoryId != null
          ? errorProvider.errors.where((e) => e.categoryId == _selectedCategoryId).toList()
          : errorProvider.errors;

      final filePath = await excelService.exportErrorsToExcel(
        errors,
        fileName: 'device_errors_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        if (filePath != null) {
          // Show success dialog with share option
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(isRTL ? 'تم التصدير بنجاح' : 'Export Successful'),
              content: Text(
                isRTL
                  ? 'تم تصدير ${errors.length} عطل إلى ملف Excel'
                  : '${errors.length} errors exported to Excel file',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(isRTL ? 'موافق' : 'OK'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    try {
                      await excelService.shareExcelFile(filePath, 'device_errors.xlsx');
                    } catch (e) {
                      if (mounted) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error sharing file: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        });
                      }
                    }
                  },
                  child: Text(isRTL ? 'مشاركة' : 'Share'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'فشل في تصدير البيانات: $e'
                : 'Export failed: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _importFromExcel() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(isRTL ? 'جاري الاستيراد...' : 'Importing...'),
            ],
          ),
        ),
      );

      final excelService = ExcelService();
      final errors = await excelService.importErrorsFromExcel();

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        if (errors.isNotEmpty) {
          // Show confirmation dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(isRTL ? 'تأكيد الاستيراد' : 'Confirm Import'),
              content: Text(
                isRTL
                  ? 'تم العثور على ${errors.length} عطل. هل تريد استيرادها؟'
                  : 'Found ${errors.length} errors. Do you want to import them?',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(isRTL ? 'إلغاء' : 'Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    await _performImport(errors);
                  },
                  child: Text(isRTL ? 'استيراد' : 'Import'),
                ),
              ],
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isRTL ? 'لم يتم العثور على أعطال في الملف' : 'No errors found in file',
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'فشل في استيراد البيانات: $e'
                : 'Import failed: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _performImport(List<DeviceError> errors) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      int successCount = 0;
      for (final error in errors) {
        final success = await errorProvider.addError(error);
        if (success) successCount++;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'تم استيراد $successCount من ${errors.length} عطل بنجاح'
                : 'Successfully imported $successCount of ${errors.length} errors',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh the errors list
        if (_selectedCategoryId != null) {
          errorProvider.fetchErrorsByCategory(_selectedCategoryId!);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _backupDatabase() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(isRTL ? 'جاري إنشاء النسخة الاحتياطية...' : 'Creating backup...'),
            ],
          ),
        ),
      );

      final backupService = BackupService();
      final filePath = await backupService.createFullBackup();

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        if (filePath != null) {
          // Show success dialog with share option
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(isRTL ? 'تم إنشاء النسخة الاحتياطية' : 'Backup Created'),
              content: Text(
                isRTL
                  ? 'تم إنشاء النسخة الاحتياطية بنجاح'
                  : 'Backup created successfully',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(isRTL ? 'موافق' : 'OK'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    try {
                      await backupService.shareBackupFile(filePath, 'database_backup.json');
                    } catch (e) {
                      if (mounted) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error sharing backup: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        });
                      }
                    }
                  },
                  child: Text(isRTL ? 'مشاركة' : 'Share'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'فشل في إنشاء النسخة الاحتياطية: $e'
                : 'Backup failed: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _restoreDatabase() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    // Show warning dialog first
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تحذير' : 'Warning'),
        content: Text(
          isRTL
            ? 'سيتم حذف جميع البيانات الحالية واستبدالها بالنسخة الاحتياطية. هل أنت متأكد؟'
            : 'All current data will be deleted and replaced with the backup. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performRestore();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'استعادة' : 'Restore'),
          ),
        ],
      ),
    );
  }

  Future<void> _performRestore() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(isRTL ? 'جاري الاستعادة...' : 'Restoring...'),
            ],
          ),
        ),
      );

      final backupService = BackupService();
      final backupData = await backupService.restoreFromBackup();

      // Restore different components
      int categoriesCount = 0;
      int errorsCount = 0;
      int manufacturersCount = 0;

      try {
        categoriesCount = await backupService.restoreCategories(backupData);
      } catch (e) {
        // Continue with other components
      }

      try {
        errorsCount = await backupService.restoreErrors(backupData);
      } catch (e) {
        // Continue with other components
      }

      try {
        manufacturersCount = await backupService.restoreManufacturersAndModels(backupData);
      } catch (e) {
        // Continue with other components
      }

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        // Show success dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(isRTL ? 'تمت الاستعادة' : 'Restore Complete'),
            content: Text(
              isRTL
                ? 'تم استعادة:\n• $categoriesCount فئة\n• $errorsCount عطل\n• $manufacturersCount شركة مصنعة'
                : 'Restored:\n• $categoriesCount categories\n• $errorsCount errors\n• $manufacturersCount manufacturers',
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Refresh all data
                  _refreshAllData();
                },
                child: Text(isRTL ? 'موافق' : 'OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'فشل في استعادة النسخة الاحتياطية: $e'
                : 'Restore failed: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _refreshAllData() {
    // Refresh all providers
    Provider.of<CategoryProvider>(context, listen: false).fetchCategories();
    Provider.of<ManufacturerProvider>(context, listen: false).fetchManufacturers();

    if (_selectedCategoryId != null) {
      Provider.of<ErrorProvider>(context, listen: false).fetchErrorsByCategory(_selectedCategoryId!);
    }
  }

  void _deleteAllErrors() async {
    if (_selectedCategoryId == null) return;

    try {
      final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
      await errorProvider.deleteAllErrorsInCategory(_selectedCategoryId!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'تم حذف جميع الأعطال بنجاح'
                : 'All errors deleted successfully',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'حدث خطأ أثناء حذف الأعطال'
                : 'Error occurred while deleting errors',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddErrorDialog(BuildContext context, [DeviceError? errorToEdit]) {
    // Get provider reference before async operation
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);

    // Navigate to full screen instead of dialog
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ErrorEditScreen(
          errorToEdit: errorToEdit,
          selectedCategoryId: _selectedCategoryId,
        ),
      ),
    ).then((_) {
      // Refresh the list when returning from edit screen
      if (mounted && _selectedCategoryId != null) {
        errorProvider.fetchErrorsByCategory(_selectedCategoryId!);
      }
    });
  }

  void _showDeleteConfirmation(BuildContext context, DeviceError error) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          isRTL ? 'تأكيد الحذف' : 'Confirm Delete',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRTL
                  ? 'هل أنت متأكد من حذف هذا العطل؟'
                  : 'Are you sure you want to delete this error?',
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer.withAlpha(128),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${error.manufacturer} ${error.model}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    isRTL ? 'كود الخطأ: ${error.errorCode}' : 'Error Code: ${error.errorCode}',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              isRTL
                  ? 'لا يمكن التراجع عن هذا الإجراء.'
                  : 'This action cannot be undone.',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => _deleteError(context, error),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  void _deleteError(BuildContext context, DeviceError error) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);

    // Close the dialog first
    navigator.pop();

    try {
      final success = await errorProvider.deleteError(error);
      if (success) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حذف العطل بنجاح' : 'Error deleted successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'فشل في حذف العطل' : 'Failed to delete error',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            isRTL ? 'خطأ في حذف العطل: $e' : 'Error deleting: $e',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Build statistics row
  Widget _buildStatsRow(BuildContext context, ErrorProvider errorProvider, LocaleProvider localeProvider) {
    final errorCount = errorProvider.errors.length;
    final isRTL = localeProvider.isRTL;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.bug_report_outlined,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'إجمالي الأعطال' : 'Total Errors',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '$errorCount',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _showStatsDialog(context),
            icon: Icon(
              Icons.analytics_outlined,
              color: Theme.of(context).colorScheme.primary,
            ),
            tooltip: isRTL ? 'عرض التفاصيل' : 'View Details',
          ),
        ],
      ),
    );
  }

  // Build empty state widget
  Widget _buildEmptyState(LocaleProvider localeProvider, String type) {
    final isRTL = localeProvider.isRTL;

    IconData icon;
    String title;
    String subtitle;
    Widget? actionButton;

    switch (type) {
      case 'select_category':
        icon = Icons.category_outlined;
        title = isRTL ? 'اختر فئة الجهاز' : 'Select Device Category';
        subtitle = isRTL
          ? 'الرجاء اختيار فئة من القائمة أعلاه لعرض الأعطال'
          : 'Please select a category from the dropdown above to view errors';
        break;
      case 'no_errors':
        icon = Icons.error_outline;
        title = isRTL ? 'لم تقم بإنشاء أي أعطال' : 'No Errors Created Yet';
        subtitle = isRTL
          ? 'لا توجد أعطال مسجلة في هذه الفئة حتى الآن'
          : 'No errors have been recorded in this category yet';
        actionButton = ElevatedButton.icon(
          onPressed: () => _showAddErrorDialog(context),
          icon: const Icon(Icons.add),
          label: Text(isRTL ? 'إضافة عطل جديد' : 'Add New Error'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        break;
      default:
        icon = Icons.info_outline;
        title = isRTL ? 'لا توجد بيانات' : 'No Data';
        subtitle = isRTL ? 'لا توجد بيانات لعرضها' : 'No data to display';
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    Color iconColor;

    switch (type) {
      case 'select_category':
        iconColor = AppTheme.primaryBlue;
        break;
      case 'no_errors':
        iconColor = AppTheme.warningAmber;
        break;
      default:
        iconColor = AppTheme.textMuted;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    iconColor.withValues(alpha: 0.1),
                    iconColor.withValues(alpha: 0.05),
                  ],
                ),
                shape: BoxShape.circle,
                border: Border.all(
                  color: iconColor.withValues(alpha: 0.2),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: iconColor.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 80,
                color: iconColor,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : AppTheme.textPrimary,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withValues(alpha: 0.05)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: 16,
                  color: isDarkMode
                      ? Colors.white.withValues(alpha: 0.7)
                      : AppTheme.textSecondary,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            if (actionButton != null) ...[
              const SizedBox(height: 32),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryBlue,
                      AppTheme.primaryBlue.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ElevatedButton.icon(
                  onPressed: () => _showAddErrorDialog(context),
                  icon: const Icon(Icons.add, color: Colors.white),
                  label: Text(
                    isRTL ? 'إضافة عطل جديد' : 'Add New Error',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Build error card
  Widget _buildErrorCard(BuildContext context, DeviceError error, LocaleProvider localeProvider) {
    final isRTL = localeProvider.isRTL;
    final isSelected = _selectedErrorIds.contains(error.id);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isSelected
          ? Theme.of(context).colorScheme.primaryContainer.withAlpha(100)
          : Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected
            ? Theme.of(context).colorScheme.primary.withAlpha(150)
            : Theme.of(context).colorScheme.outline.withAlpha(50),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(100)
              : Colors.grey.withAlpha(80),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            if (_isSelectionMode) {
              _toggleErrorSelection(error.id);
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ErrorDetailsScreen(errorId: error.id),
                ),
              );
            }
          },
          onLongPress: () {
            if (!_isSelectionMode) {
              _enterSelectionMode();
              _toggleErrorSelection(error.id);
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    // Selection indicator
                    if (_isSelectionMode) ...[
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected
                            ? Colors.blue.shade600
                            : Colors.grey.shade300,
                          border: Border.all(
                            color: isSelected
                              ? Colors.blue.shade600
                              : Colors.grey.shade400,
                            width: 2,
                          ),
                        ),
                        child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                      ),
                      const SizedBox(width: 12),
                    ],
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.devices_outlined,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${error.manufacturer} - ${error.model}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.shade100,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              error.errorCode,
                              style: TextStyle(
                                color: Colors.orange.shade800,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (!_isSelectionMode)
                      PopupMenuButton<String>(
                        onSelected: (value) {
                          switch (value) {
                            case 'edit':
                              _showAddErrorDialog(context, error);
                              break;
                            case 'delete':
                              _showDeleteConfirmation(context, error);
                              break;
                            case 'view':
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ErrorDetailsScreen(errorId: error.id),
                                ),
                              );
                              break;
                          }
                        },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'view',
                          child: Row(
                            children: [
                              const Icon(Icons.visibility_outlined, size: 18),
                              const SizedBox(width: 8),
                              Text(isRTL ? 'عرض' : 'View'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              const Icon(Icons.edit_outlined, size: 18),
                              const SizedBox(width: 8),
                              Text(isRTL ? 'تعديل' : 'Edit'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete_outline, size: 18, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                isRTL ? 'حذف' : 'Delete',
                                style: const TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.more_vert,
                          color: Colors.grey.shade600,
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  error.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade700,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Footer with attachments and date
                Row(
                  children: [
                    if (error.attachments.isNotEmpty) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade100,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.attach_file,
                              size: 14,
                              color: Colors.blue.shade700,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${error.attachments.length}',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    Expanded(
                      child: Text(
                        _formatDate(error.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade500,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: Colors.grey.shade400,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Show statistics dialog
  void _showStatsDialog(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.analytics_outlined,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(isRTL ? 'إحصائيات الأعطال' : 'Error Statistics'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatItem(
              context,
              Icons.bug_report_outlined,
              isRTL ? 'إجمالي الأعطال' : 'Total Errors',
              '${errorProvider.errors.length}',
              Colors.blue,
            ),
            const SizedBox(height: 12),
            _buildStatItem(
              context,
              Icons.attach_file,
              isRTL ? 'الأعطال مع مرفقات' : 'Errors with Attachments',
              '${errorProvider.errors.where((e) => e.attachments.isNotEmpty).length}',
              Colors.green,
            ),
            const SizedBox(height: 12),
            _buildStatItem(
              context,
              Icons.today,
              isRTL ? 'أعطال اليوم' : 'Today\'s Errors',
              '${errorProvider.errors.where((e) => _isToday(e.createdAt)).length}',
              Colors.orange,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إغلاق' : 'Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  // Selection mode methods
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedErrorIds.clear();
    });
  }

  void _toggleErrorSelection(String errorId) {
    setState(() {
      if (_selectedErrorIds.contains(errorId)) {
        _selectedErrorIds.remove(errorId);
      } else {
        _selectedErrorIds.add(errorId);
      }
    });
  }

  void _toggleSelectAll() {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    setState(() {
      if (_selectedErrorIds.length == errorProvider.errors.length) {
        _selectedErrorIds.clear();
      } else {
        _selectedErrorIds.clear();
        _selectedErrorIds.addAll(errorProvider.errors.map((e) => e.id));
      }
    });
  }

  void _deleteSelectedErrors() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (_selectedErrorIds.isEmpty) return;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تأكيد الحذف' : 'Confirm Delete'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف ${_selectedErrorIds.length} عطل؟ هذا الإجراء لا يمكن التراجع عنه.'
            : 'Are you sure you want to delete ${_selectedErrorIds.length} errors? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // Show loading dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 16),
                Text(isRTL ? 'جاري الحذف...' : 'Deleting...'),
              ],
            ),
          ),
        );
      }

      int successCount = 0;
      for (final errorId in _selectedErrorIds) {
        final success = await errorProvider.deleteError(errorId);
        if (success) successCount++;
      }

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'تم حذف $successCount من ${_selectedErrorIds.length} عطل بنجاح'
                : 'Successfully deleted $successCount of ${_selectedErrorIds.length} errors',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Exit selection mode and refresh
        _exitSelectionMode();
        if (_selectedCategoryId != null) {
          errorProvider.fetchErrorsByCategory(_selectedCategoryId!);
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'حدث خطأ أثناء الحذف: $e'
                : 'Error occurred while deleting: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareSelectedErrors() async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (_selectedErrorIds.isEmpty) return;

    try {
      // Get selected errors
      final selectedErrors = errorProvider.errors
          .where((error) => _selectedErrorIds.contains(error.id))
          .toList();

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(isRTL ? 'جاري التصدير...' : 'Exporting...'),
            ],
          ),
        ),
      );

      final excelService = ExcelService();
      final filePath = await excelService.exportErrorsToExcel(
        selectedErrors,
        fileName: 'selected_errors_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (mounted) {
        Navigator.pop(context); // Close loading dialog

        if (filePath != null) {
          // Share the file
          await excelService.shareExcelFile(filePath, 'selected_errors.xlsx');

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  isRTL
                    ? 'تم تصدير ${selectedErrors.length} عطل بنجاح'
                    : '${selectedErrors.length} errors exported successfully',
                ),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                ? 'فشل في تصدير البيانات: $e'
                : 'Export failed: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}



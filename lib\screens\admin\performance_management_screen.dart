import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/modern_gradient_background.dart';
import '../../utils/theme.dart';
import '../../services/performance_service.dart';
import '../../services/app_optimization_service.dart';
import '../../widgets/responsive_layout.dart';

class PerformanceManagementScreen extends StatefulWidget {
  const PerformanceManagementScreen({super.key});

  @override
  State<PerformanceManagementScreen> createState() => _PerformanceManagementScreenState();
}

class _PerformanceManagementScreenState extends State<PerformanceManagementScreen> {
  bool _isLoading = false;
  Map<String, dynamic> _performanceStats = {};
  
  @override
  void initState() {
    super.initState();
    _loadPerformanceStats();
  }

  Future<void> _loadPerformanceStats() async {
    setState(() => _isLoading = true);
    try {
      final stats = await AppOptimizationService.instance.getPerformanceStats();
      setState(() => _performanceStats = stats);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل إحصائيات الأداء: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _performFullCleanup() async {
    setState(() => _isLoading = true);
    try {
      await AppOptimizationService.instance.performFullCleanup();
      await _loadPerformanceStats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم تنظيف التطبيق بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تنظيف التطبيق: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _optimizeForLowEndDevice() async {
    setState(() => _isLoading = true);
    try {
      await AppOptimizationService.instance.optimizeForLowEndDevice();
      await _loadPerformanceStats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم تحسين التطبيق للأجهزة الضعيفة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في التحسين: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      body: ModernGradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        isRTL ? Icons.arrow_forward : Icons.arrow_back,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'إدارة الأداء والحجم',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: isRTL ? TextAlign.right : TextAlign.left,
                      ),
                    ),
                    IconButton(
                      onPressed: _loadPerformanceStats,
                      icon: const Icon(
                        Icons.refresh,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: ResponsiveLayout(
                  mobile: _buildMobileLayout(),
                  tablet: _buildTabletLayout(),
                  desktop: _buildDesktopLayout(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildPerformanceStatsCard(),
          const SizedBox(height: 16),
          _buildOptimizationActionsCard(),
          const SizedBox(height: 16),
          _buildQuickActionsCard(),
        ],
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: _buildPerformanceStatsCard()),
              const SizedBox(width: 16),
              Expanded(child: _buildOptimizationActionsCard()),
            ],
          ),
          const SizedBox(height: 24),
          _buildQuickActionsCard(),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 2, child: _buildPerformanceStatsCard()),
              const SizedBox(width: 24),
              Expanded(child: _buildOptimizationActionsCard()),
            ],
          ),
          const SizedBox(height: 32),
          _buildQuickActionsCard(),
        ],
      ),
    );
  }

  Widget _buildPerformanceStatsCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white.withOpacity(0.9),
              Colors.white.withOpacity(0.7),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: AppTheme.primaryColor, size: 28),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات الأداء',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_performanceStats.isEmpty)
              const Text('لا توجد إحصائيات متاحة')
            else
              ..._performanceStats.entries.map((entry) => _buildStatItem(entry.key, entry.value.toString())),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    String displayLabel = label;
    switch (label) {
      case 'memory_usage_mb':
        displayLabel = 'استخدام الذاكرة (MB)';
        break;
      case 'image_cache_size_mb':
        displayLabel = 'حجم كاش الصور (MB)';
        break;
      case 'image_cache_count':
        displayLabel = 'عدد الصور المخزنة';
        break;
      case 'max_image_cache_mb':
        displayLabel = 'الحد الأقصى لكاش الصور (MB)';
        break;
      case 'is_optimized':
        displayLabel = 'حالة التحسين';
        value = value == 'true' ? 'مُحسَّن' : 'غير مُحسَّن';
        break;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            displayLabel,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationActionsCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade50,
              Colors.blue.shade100,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.tune, color: AppTheme.primaryColor, size: 28),
                const SizedBox(width: 12),
                const Text(
                  'إجراءات التحسين',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildActionButton(
              'تنظيف شامل',
              'تنظيف الملفات المؤقتة والكاش',
              Icons.cleaning_services,
              _performFullCleanup,
              Colors.green,
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'تحسين للأجهزة الضعيفة',
              'تقليل استخدام الذاكرة والموارد',
              Icons.memory,
              _optimizeForLowEndDevice,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.purple.shade50,
              Colors.purple.shade100,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: AppTheme.primaryColor, size: 28),
                const SizedBox(width: 12),
                const Text(
                  'إجراءات سريعة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              'نصائح لتحسين الأداء:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            _buildTipItem('• قم بإعادة تشغيل التطبيق بانتظام'),
            _buildTipItem('• احذف الملفات غير المستخدمة'),
            _buildTipItem('• تجنب فتح عدة شاشات في نفس الوقت'),
            _buildTipItem('• استخدم التنظيف الشامل أسبوعياً'),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, String subtitle, IconData icon, VoidCallback onPressed, Color color) {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          elevation: 4,
        ),
        child: Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      opacity: 0.9,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
      ),
    );
  }
}

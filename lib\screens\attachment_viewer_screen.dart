import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import '../models/attachment_model.dart';
import '../providers/attachment_provider.dart';
import '../widgets/cached_attachment_widget.dart';
import '../widgets/google_drive_image_widget.dart';
import '../services/attachment_cache_service.dart';

/// شاشة محسنة لعرض المرفقات مع خيارات متقدمة
class AttachmentViewerScreen extends StatefulWidget {
  final AttachmentModel attachment;
  final List<AttachmentModel>? allAttachments;
  final int? initialIndex;

  const AttachmentViewerScreen({
    super.key,
    required this.attachment,
    this.allAttachments,
    this.initialIndex,
  });

  @override
  State<AttachmentViewerScreen> createState() => _AttachmentViewerScreenState();
}

class _AttachmentViewerScreenState extends State<AttachmentViewerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  bool _isLoading = false;
  bool _showAppBar = true;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex ?? 0;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  List<AttachmentModel> get _attachments => widget.allAttachments ?? [widget.attachment];
  AttachmentModel get _currentAttachment => _attachments[_currentIndex];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: _showAppBar ? _buildAppBar() : null,
      body: GestureDetector(
        onTap: () {
          setState(() {
            _showAppBar = !_showAppBar;
          });
        },
        child: Stack(
          children: [
            _buildContent(),
            if (_attachments.length > 1) _buildNavigationIndicator(),
            if (_isLoading) _buildLoadingOverlay(),
          ],
        ),
      ),
      bottomNavigationBar: _showAppBar ? _buildBottomBar() : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.black.withValues(alpha: 0.7),
      iconTheme: const IconThemeData(color: Colors.white),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentAttachment.fileName,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            _formatFileSize(_currentAttachment.fileSize),
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
        ],
      ),
      actions: [
        // مؤشر الـ cache
        Consumer<AttachmentProvider>(
          builder: (context, provider, child) {
            return FutureBuilder<bool>(
              future: provider.isAttachmentCached(_currentAttachment),
              builder: (context, snapshot) {
                if (snapshot.data == true) {
                  return const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Icon(
                      Icons.offline_pin,
                      color: Colors.green,
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            );
          },
        ),
        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'download',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('تحميل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'copy_link',
              child: Row(
                children: [
                  Icon(Icons.link),
                  SizedBox(width: 8),
                  Text('نسخ الرابط'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'open_external',
              child: Row(
                children: [
                  Icon(Icons.open_in_new),
                  SizedBox(width: 8),
                  Text('فتح خارجياً'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'details',
              child: Row(
                children: [
                  Icon(Icons.info),
                  SizedBox(width: 8),
                  Text('التفاصيل'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_attachments.length == 1) {
      return _buildSingleAttachment(_currentAttachment);
    }

    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      itemCount: _attachments.length,
      itemBuilder: (context, index) {
        return _buildSingleAttachment(_attachments[index]);
      },
    );
  }

  Widget _buildSingleAttachment(AttachmentModel attachment) {
    if (attachment.isImage) {
      return _buildImageViewer(attachment);
    } else if (attachment.isVideo) {
      return _buildVideoViewer(attachment);
    } else {
      return _buildDocumentViewer(attachment);
    }
  }

  Widget _buildImageViewer(AttachmentModel attachment) {
    return Center(
      child: InteractiveViewer(
        minScale: 0.5,
        maxScale: 5.0,
        child: attachment.url.contains('drive.google.com')
            ? GoogleDriveImageWidget(
                imageUrl: attachment.url,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.contain,
              )
            : CachedAttachmentWidget(
                attachment: attachment,
                fit: BoxFit.contain,
              ),
      ),
    );
  }

  Widget _buildVideoViewer(AttachmentModel attachment) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 100,
            color: Colors.white.withValues(alpha: 0.8),
          ),
          const SizedBox(height: 16),
          Text(
            attachment.fileName,
            style: const TextStyle(color: Colors.white, fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _formatFileSize(attachment.fileSize),
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _openUrl(attachment.url),
            icon: const Icon(Icons.play_arrow),
            label: const Text('تشغيل الفيديو'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentViewer(AttachmentModel attachment) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getDocumentIcon(attachment.fileExtension),
            size: 100,
            color: _getDocumentColor(attachment.fileExtension),
          ),
          const SizedBox(height: 16),
          Text(
            attachment.fileName,
            style: const TextStyle(color: Colors.white, fontSize: 18),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _formatFileSize(attachment.fileSize),
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _openUrl(attachment.url),
            icon: const Icon(Icons.open_in_new),
            label: const Text('فتح المستند'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationIndicator() {
    if (_attachments.length <= 1) return const SizedBox.shrink();

    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '${_currentIndex + 1} / ${_attachments.length}',
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: const Center(
        child: CircularProgressIndicator(color: Colors.white),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: () => _handleMenuAction('download'),
            icon: const Icon(Icons.download, color: Colors.white),
            tooltip: 'تحميل',
          ),
          IconButton(
            onPressed: () => _handleMenuAction('share'),
            icon: const Icon(Icons.share, color: Colors.white),
            tooltip: 'مشاركة',
          ),
          IconButton(
            onPressed: () => _handleMenuAction('copy_link'),
            icon: const Icon(Icons.link, color: Colors.white),
            tooltip: 'نسخ الرابط',
          ),
          IconButton(
            onPressed: () => _handleMenuAction('open_external'),
            icon: const Icon(Icons.open_in_new, color: Colors.white),
            tooltip: 'فتح خارجياً',
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) async {
    switch (action) {
      case 'download':
        await _downloadAttachment();
        break;
      case 'share':
        await _shareAttachment();
        break;
      case 'copy_link':
        await _copyLink();
        break;
      case 'open_external':
        await _openUrl(_currentAttachment.url);
        break;
      case 'details':
        _showDetailsDialog();
        break;
    }
  }

  Future<void> _downloadAttachment() async {
    setState(() => _isLoading = true);

    try {
      final provider = Provider.of<AttachmentProvider>(context, listen: false);
      await provider.cacheAttachment(_currentAttachment);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحميل ${_currentAttachment.fileName} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحميل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _shareAttachment() async {
    try {
      final cachedFile = await AttachmentCacheService.instance.getCachedFile(_currentAttachment);

      if (cachedFile != null && await cachedFile.exists()) {
        await Share.shareXFiles(
          [XFile(cachedFile.path)],
          text: _currentAttachment.fileName,
        );
      } else {
        // مشاركة الرابط إذا لم يكن الملف محفوظ محلياً
        await Share.share(
          _currentAttachment.url,
          subject: _currentAttachment.fileName,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في المشاركة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _copyLink() async {
    await Clipboard.setData(ClipboardData(text: _currentAttachment.url));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ الرابط'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _openUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح الرابط';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDetailsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل المرفق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الاسم', _currentAttachment.fileName),
            _buildDetailRow('النوع', _currentAttachment.type.name),
            _buildDetailRow('الحجم', _formatFileSize(_currentAttachment.fileSize)),
            _buildDetailRow('الامتداد', _currentAttachment.fileExtension),
            _buildDetailRow('تاريخ الرفع', _formatDate(_currentAttachment.uploadedAt)),
            const SizedBox(height: 16),
            Consumer<AttachmentProvider>(
              builder: (context, provider, child) {
                return FutureBuilder<bool>(
                  future: provider.isAttachmentCached(_currentAttachment),
                  builder: (context, snapshot) {
                    final isCached = snapshot.data ?? false;
                    return Row(
                      children: [
                        Icon(
                          isCached ? Icons.offline_pin : Icons.cloud_download,
                          color: isCached ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isCached ? 'محفوظ محلياً' : 'غير محفوظ محلياً',
                          style: TextStyle(
                            color: isCached ? Colors.green : Colors.orange,
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  IconData _getDocumentIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'txt':
        return Icons.text_snippet;
      case 'rtf':
        return Icons.article;
      case 'odt':
        return Icons.description;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getDocumentColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'txt':
        return Colors.grey;
      case 'rtf':
        return Colors.orange;
      case 'odt':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

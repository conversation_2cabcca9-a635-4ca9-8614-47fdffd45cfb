import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../models/device_error_model.dart';
import '../../models/attachment_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/error_provider.dart';
import '../../providers/locale_provider.dart';
import '../../widgets/image_upload_widget.dart';
import '../../widgets/attachment_grid_widget.dart';
import '../../widgets/attachment_picker_widget.dart';
import '../../widgets/gradient_background.dart';
import '../../providers/attachment_provider.dart';
import '../../services/file_download_service.dart';
import '../../services/cloud_upload_service.dart';
import '../admin/error_edit_screen.dart';

class ErrorDetailsScreen extends StatefulWidget {
  final String errorId;

  const ErrorDetailsScreen({super.key, required this.errorId});

  @override
  State<ErrorDetailsScreen> createState() => _ErrorDetailsScreenState();
}

class _ErrorDetailsScreenState extends State<ErrorDetailsScreen> {
  late Future<DeviceError?> _errorFuture;

  @override
  void initState() {
    super.initState();
    _loadError();
    _preloadAttachments();
  }

  Future<void> _preloadAttachments() async {
    try {
      final error = await _errorFuture;
      if (error != null && error.attachments.isNotEmpty && mounted) {
        final attachmentProvider = Provider.of<AttachmentProvider>(context, listen: false);
        await attachmentProvider.preloadAttachments(error.attachments);
      }
    } catch (e) {
      debugPrint('Error preloading attachments: $e');
    }
  }

  void _loadError() {
    _errorFuture = Provider.of<ErrorProvider>(context, listen: false)
        .getErrorById(widget.errorId);
  }

  Future<void> _toggleFavorite(DeviceError error) async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL
                ? 'يجب تسجيل الدخول لإضافة العطل إلى المفضلة'
                : 'You must be logged in to add to favorites',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final success = await errorProvider.toggleFavorite(error);

      if (success && mounted) {
        // Reload the error
        setState(() {
          _loadError();
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              error.isFavorite
                  ? (isRTL ? 'تمت إزالة العطل من المفضلة' : 'Removed from favorites')
                  : (isRTL ? 'تمت إضافة العطل إلى المفضلة' : 'Added to favorites'),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEditDialog(BuildContext context, DeviceError error) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ErrorEditScreen(
          errorToEdit: error,
          selectedCategoryId: error.categoryId,
        ),
      ),
    ).then((_) {
      // Reload the error when returning from edit screen
      setState(() {
        _loadError();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return GradientScaffold(
      extendBodyBehindAppBar: false,
      resizeToAvoidBottomInset: true,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary,
                Theme.of(context).colorScheme.primary.withAlpha(230),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: AppBar(
            title: Text(
              isRTL ? 'تفاصيل العطل' : 'Error Details',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            actions: [
              FutureBuilder<DeviceError?>(
            future: _errorFuture,
            builder: (context, snapshot) {
              if (!snapshot.hasData || snapshot.data == null) {
                return const SizedBox.shrink();
              }

              final error = snapshot.data!;
              final isAdmin = Provider.of<AuthProvider>(context).isAdmin;

              // If admin has multiple actions, use popup menu
              if (isAdmin) {
                return PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'favorite':
                        _toggleFavorite(error);
                        break;
                      case 'edit':
                        _showEditDialog(context, error);
                        break;
                      case 'delete':
                        _showDeleteConfirmation(context, error);
                        break;
                    }
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 8,
                  shadowColor: Theme.of(context).shadowColor.withAlpha(100),
                  offset: const Offset(0, 8),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'favorite',
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: error.isFavorite
                                    ? LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          Colors.red.withAlpha(200),
                                          Colors.red.shade600.withAlpha(180),
                                        ],
                                      )
                                    : null,
                                color: error.isFavorite
                                    ? null
                                    : Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(100),
                                borderRadius: BorderRadius.circular(10),
                                border: error.isFavorite
                                    ? Border.all(
                                        color: Colors.red.withAlpha(100),
                                        width: 1,
                                      )
                                    : null,
                                boxShadow: error.isFavorite
                                    ? [
                                        BoxShadow(
                                          color: Colors.red.withAlpha(40),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]
                                    : null,
                              ),
                              child: Icon(
                                error.isFavorite ? Icons.favorite : Icons.favorite_border,
                                color: error.isFavorite
                                    ? Colors.white
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                                size: 18,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                isRTL
                                    ? (error.isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة')
                                    : (error.isFavorite ? 'Remove from favorites' : 'Add to favorites'),
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  color: error.isFavorite ? Colors.red : null,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withAlpha(30),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.edit,
                                color: Theme.of(context).colorScheme.primary,
                                size: 18,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                isRTL ? 'تعديل' : 'Edit',
                                style: const TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.error.withAlpha(30),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.delete,
                                color: Theme.of(context).colorScheme.error,
                                size: 18,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                isRTL ? 'حذف' : 'Delete',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(100),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.more_vert,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                  tooltip: isRTL ? 'المزيد من الخيارات' : 'More options',
                );
              } else {
                // For non-admin users, show enhanced favorite button
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    gradient: error.isFavorite
                        ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.red.withAlpha(240),
                              Colors.red.shade600.withAlpha(220),
                            ],
                          )
                        : null,
                    color: error.isFavorite
                        ? null
                        : Theme.of(context).colorScheme.surface.withAlpha(100),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: error.isFavorite
                          ? Colors.white.withAlpha(100)
                          : Theme.of(context).colorScheme.outline.withAlpha(80),
                      width: 1.5,
                    ),
                    boxShadow: error.isFavorite
                        ? [
                            BoxShadow(
                              color: Colors.red.withAlpha(60),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                              spreadRadius: 1,
                            ),
                          ]
                        : [
                            BoxShadow(
                              color: Colors.black.withAlpha(20),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(24),
                      onTap: () => _toggleFavorite(error),
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          error.isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: error.isFavorite
                              ? Colors.white
                              : Theme.of(context).colorScheme.onSurface.withAlpha(160),
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                );
              }
            },
          ),
            ],
          ),
        ),
      ),
      body: FutureBuilder<DeviceError?>(
        future: _errorFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ: ${snapshot.error}',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('العودة'),
                  ),
                ],
              ),
            );
          }

          if (!snapshot.hasData || snapshot.data == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.info, size: 48, color: Colors.orange),
                  const SizedBox(height: 16),
                  const Text(
                    'لم يتم العثور على العطل',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('العودة'),
                  ),
                ],
              ),
            );
          }

          final error = snapshot.data!;
          final isAdmin = Provider.of<AuthProvider>(context).isAdmin;

          return SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                // Header
                Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: Theme.of(context).brightness == Brightness.dark
                          ? [
                              const Color(0xFF1E293B).withAlpha(240),
                              const Color(0xFF334155).withAlpha(220),
                            ]
                          : [
                              Colors.white.withAlpha(250),
                              const Color(0xFFF8FAFF).withAlpha(240),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withAlpha(100),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.black.withAlpha(150)
                            : Colors.grey.withAlpha(100),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 1,
                      ),
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withAlpha(20),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                        spreadRadius: -2,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Theme.of(context).colorScheme.primary,
                                    Theme.of(context).colorScheme.primary.withAlpha(200),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Theme.of(context).colorScheme.primary.withAlpha(60),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.devices,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${error.manufacturer} - ${error.model}',
                                    style: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).colorScheme.onSurface,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    isRTL ? 'معلومات الجهاز' : 'Device Information',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                Colors.red.withAlpha(200),
                                Colors.red.shade600.withAlpha(180),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withAlpha(40),
                                blurRadius: 8,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'كود الخطأ: ${error.errorCode}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Description
                Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: Theme.of(context).brightness == Brightness.dark
                          ? [
                              const Color(0xFF1E293B).withAlpha(240),
                              const Color(0xFF334155).withAlpha(220),
                            ]
                          : [
                              Colors.white.withAlpha(250),
                              const Color(0xFFF8FAFF).withAlpha(240),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withAlpha(100),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.black.withAlpha(150)
                            : Colors.grey.withAlpha(100),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: Colors.orange.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.description,
                                color: Colors.orange,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'وصف العطل',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(50),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline.withAlpha(80),
                            ),
                          ),
                          child: Text(
                            error.description,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.5,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Solution
                Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: Theme.of(context).brightness == Brightness.dark
                          ? [
                              const Color(0xFF1E293B).withAlpha(240),
                              const Color(0xFF334155).withAlpha(220),
                            ]
                          : [
                              Colors.white.withAlpha(250),
                              const Color(0xFFF8FAFF).withAlpha(240),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withAlpha(100),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.black.withAlpha(150)
                            : Colors.grey.withAlpha(100),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: Colors.green.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.lightbulb,
                                color: Colors.green,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'الحل المقترح',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(50),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline.withAlpha(80),
                            ),
                          ),
                          child: Text(
                            error.solution,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.5,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Images
                if (error.imageUrls.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline.withAlpha(150),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.black.withAlpha(120)
                              : Colors.grey.withAlpha(80),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Card(
                      elevation: 0,
                      margin: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'الصور التوضيحية',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (isAdmin)
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () => _showImageManagementDialog(context, error),
                                  tooltip: isRTL ? 'إدارة الصور' : 'Manage Images',
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            height: 200,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: error.imageUrls.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: Stack(
                                    children: [
                                      GestureDetector(
                                        onTap: () => _showImageDialog(context, error.imageUrls, index),
                                        child: Hero(
                                          tag: 'image_${error.id}_$index',
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(8),
                                            child: CachedNetworkImage(
                                              imageUrl: error.imageUrls[index],
                                              width: 200,
                                              height: 200,
                                              fit: BoxFit.cover,
                                              httpHeaders: const {
                                                'User-Agent': 'HMDeviceErrors/1.0',
                                                'Accept': 'image/*',
                                              },
                                              placeholder: (context, url) => Container(
                                                width: 200,
                                                height: 200,
                                                color: Colors.grey[300],
                                                child: const Center(
                                                  child: CircularProgressIndicator(),
                                                ),
                                              ),
                                              errorWidget: (context, url, error) => Container(
                                                width: 200,
                                                height: 200,
                                                color: Colors.grey[300],
                                                child: const Center(
                                                  child: Column(
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      Icon(Icons.error, color: Colors.red),
                                                      SizedBox(height: 4),
                                                      Text(
                                                        'Failed to load',
                                                        style: TextStyle(fontSize: 12),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // Admin delete button
                                      ...Provider.of<AuthProvider>(context, listen: false).isAdmin
                                          ? [
                                              Positioned(
                                                top: 4,
                                                right: 4,
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    color: Colors.red.withValues(alpha: 0.8),
                                                    borderRadius: BorderRadius.circular(12),
                                                  ),
                                                  child: IconButton(
                                                    icon: const Icon(Icons.delete, color: Colors.white, size: 16),
                                                    onPressed: () => _deleteImage(context, error, index),
                                                    padding: const EdgeInsets.all(4),
                                                    constraints: const BoxConstraints(
                                                      minWidth: 24,
                                                      minHeight: 24,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ]
                                          : [],
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                      ),
                    ),
                  ),

                const SizedBox(height: 16),

                // Attachments (new format)
                if (error.attachments.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline.withAlpha(150),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.black.withAlpha(120)
                              : Colors.grey.withAlpha(80),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Card(
                      elevation: 0,
                      margin: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).colorScheme.primary.withAlpha(30),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.attach_file,
                                    color: Theme.of(context).colorScheme.primary,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        isRTL ? 'المرفقات' : 'Attachments',
                                        style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        isRTL
                                          ? '${error.attachments.length} ملف مرفق'
                                          : '${error.attachments.length} attached files',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                if (isAdmin)
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(100),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: IconButton(
                                      icon: const Icon(Icons.edit, size: 20),
                                      onPressed: () => _showImageManagementDialog(context, error),
                                      tooltip: isRTL ? 'إدارة المرفقات' : 'Manage Attachments',
                                      style: IconButton.styleFrom(
                                        foregroundColor: Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(50),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.outline.withAlpha(100),
                                ),
                              ),
                              child: AttachmentGridWidget(
                                attachments: error.attachments,
                                showActions: isAdmin,
                                onDelete: isAdmin ? (attachment) => _deleteAttachment(context, error, attachment) : null,
                                onDownload: (attachment) => _downloadAttachment(attachment),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                const SizedBox(height: 16),

                // Metadata
                Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: Theme.of(context).brightness == Brightness.dark
                          ? [
                              const Color(0xFF1E293B).withAlpha(240),
                              const Color(0xFF334155).withAlpha(220),
                            ]
                          : [
                              Colors.white.withAlpha(250),
                              const Color(0xFFF8FAFF).withAlpha(240),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withAlpha(100),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.black.withAlpha(150)
                            : Colors.grey.withAlpha(100),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: Colors.purple.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.info,
                                color: Colors.purple,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'معلومات إضافية',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(50),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline.withAlpha(80),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    size: 18,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'تاريخ الإضافة: ${_formatDate(error.createdAt)}',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Theme.of(context).colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                              if (error.updatedAt != null) ...[
                                const SizedBox(height: 12),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.update,
                                      size: 18,
                                      color: Theme.of(context).colorScheme.secondary,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تاريخ التحديث: ${_formatDate(error.updatedAt!)}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context).colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          ),
          );
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  void _showImageDialog(BuildContext context, List<String> imageUrls, int initialIndex) {
    showDialog(
      context: context,
      builder: (context) => _ImageViewerDialog(
        imageUrls: imageUrls,
        initialIndex: initialIndex,
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, DeviceError error) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isRTL ? 'تأكيد الحذف' : 'Confirm Delete'),
          content: Text(
            isRTL
                ? 'هل أنت متأكد من حذف هذا العطل؟\n${error.manufacturer} ${error.model} - ${error.errorCode}'
                : 'Are you sure you want to delete this error?\n${error.manufacturer} ${error.model} - ${error.errorCode}',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(isRTL ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteError(error);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Colors.white,
              ),
              child: Text(isRTL ? 'حذف' : 'Delete'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteError(DeviceError error) async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      final success = await errorProvider.deleteError(error.id);

      if (success && mounted) {
        // Navigate back to errors list
        Navigator.of(context).pop();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حذف العطل بنجاح' : 'Error deleted successfully',
            ),
            backgroundColor: Theme.of(context).colorScheme.tertiary,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'فشل في حذف العطل' : 'Failed to delete error',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showImageManagementDialog(BuildContext context, DeviceError error) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.attach_file, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            Text(isRTL ? 'إدارة المرفقات' : 'Manage Attachments'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(30),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.add_photo_alternate, color: Colors.green),
              ),
              title: Text(isRTL ? 'إضافة مرفقات جديدة' : 'Add New Attachments'),
              subtitle: Text(
                isRTL ? 'صور، فيديوهات، مستندات' : 'Images, Videos, Documents',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
              onTap: () {
                Navigator.pop(context);
                _showAttachmentPickerDialog(context, error);
              },
            ),
            const Divider(),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(30),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.edit, color: Colors.blue),
              ),
              title: Text(isRTL ? 'تعديل المرفقات' : 'Edit Attachments'),
              subtitle: Text(
                isRTL ? 'حذف أو إعادة ترتيب' : 'Delete or Reorder',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              ),
              onTap: () {
                Navigator.pop(context);
                _editAttachments(context, error);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  void _deleteImage(BuildContext context, DeviceError error, int index) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'حذف الصورة' : 'Delete Image'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف هذه الصورة؟'
            : 'Are you sure you want to delete this image?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDeleteImage(error, index);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteImage(DeviceError error, int index) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      final updatedImageUrls = List<String>.from(error.imageUrls);
      updatedImageUrls.removeAt(index);

      final updatedError = error.copyWith(
        imageUrls: updatedImageUrls,
        updatedAt: DateTime.now(),
      );

      final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
      final success = await errorProvider.updateError(updatedError);

      if (success && mounted) {
        setState(() {
          _loadError();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حذف الصورة بنجاح' : 'Image deleted successfully',
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'فشل في حذف الصورة' : 'Failed to delete image',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show attachment picker dialog with unsaved changes protection
  void _showAttachmentPickerDialog(BuildContext context, DeviceError error) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => _AttachmentPickerDialog(
        error: error,
        onAttachmentsAdded: (List<AttachmentModel> newAttachments) async {
          if (newAttachments.isNotEmpty) {
            // Save references before async operations
            final scaffoldMessenger = ScaffoldMessenger.of(context);

            // Update the error with new attachments
            final updatedError = error.copyWith(
              attachments: [...error.attachments, ...newAttachments],
              updatedAt: DateTime.now(),
            );

            final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
            final success = await errorProvider.updateError(updatedError);

            if (success && mounted) {
              // Reload the error to reflect changes
              setState(() {
                _loadError();
              });

              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text(
                    isRTL
                        ? 'تم إضافة ${newAttachments.length} مرفق بنجاح'
                        : '${newAttachments.length} attachment(s) added successfully',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            }
          }
        },
      ),
    );
  }

  // Edit existing attachments
  void _editAttachments(BuildContext context, DeviceError error) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => _AttachmentEditDialog(
        error: error,
        onAttachmentsUpdated: (List<AttachmentModel> updatedAttachments) async {
          // Save references before async operations
          final scaffoldMessenger = ScaffoldMessenger.of(context);

          // Update the error with modified attachments
          final updatedError = error.copyWith(
            attachments: updatedAttachments,
            updatedAt: DateTime.now(),
          );

          final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
          final success = await errorProvider.updateError(updatedError);

          if (success && mounted) {
            // Reload the error to reflect changes
            setState(() {
              _loadError();
            });

            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  isRTL
                      ? 'تم تحديث المرفقات بنجاح'
                      : 'Attachments updated successfully',
                ),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
      ),
    );
  }

  // Delete attachment from error
  Future<void> _deleteAttachment(BuildContext context, DeviceError error, AttachmentModel attachment) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    // Save references before async operations
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تأكيد الحذف' : 'Confirm Delete'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من حذف هذا المرفق؟\n${attachment.originalFileName}'
            : 'Are you sure you want to delete this attachment?\n${attachment.originalFileName}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Step 1: Delete from cloud storage first
        bool cloudDeleteSuccess = false;
        try {
          cloudDeleteSuccess = await CloudUploadService.deleteFile(attachment.url);

          // Also delete thumbnail if exists
          if (attachment.thumbnailUrl != null && attachment.thumbnailUrl!.isNotEmpty) {
            await CloudUploadService.deleteFile(attachment.thumbnailUrl!);
          }
        } catch (e) {
          debugPrint('Warning: Failed to delete from cloud storage: $e');
          // Continue with database deletion even if cloud deletion fails
        }

        // Step 2: Remove attachment from error (use index-based removal for safety)
        debugPrint('Deleting attachment: ${attachment.fileName}');
        debugPrint('Current attachments count: ${error.attachments.length}');

        // Find the attachment by fileName and URL to be more reliable than ID
        final updatedAttachments = error.attachments.where((a) {
          final isSameFile = a.fileName == attachment.fileName && a.url == attachment.url;
          debugPrint('Checking attachment: ${a.fileName} vs ${attachment.fileName}, same: $isSameFile');
          return !isSameFile;
        }).toList();

        debugPrint('Updated attachments count: ${updatedAttachments.length}');
        final updatedError = error.copyWith(attachments: updatedAttachments);

        // Step 3: Update error in database
        final success = await errorProvider.updateError(updatedError);

        if (success && mounted) {
          // Reload error to reflect changes
          setState(() {
            _loadError();
          });

          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                cloudDeleteSuccess
                  ? (isRTL ? 'تم حذف المرفق بنجاح من قاعدة البيانات والتخزين السحابي' : 'Attachment deleted successfully from database and cloud storage')
                  : (isRTL ? 'تم حذف المرفق من قاعدة البيانات (تحذير: قد يبقى في التخزين السحابي)' : 'Attachment deleted from database (Warning: may remain in cloud storage)'),
              ),
              backgroundColor: cloudDeleteSuccess ? Colors.green : Colors.orange,
            ),
          );
        } else if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'فشل في حذف المرفق من قاعدة البيانات' : 'Failed to delete attachment from database'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(isRTL ? 'فشل في حذف المرفق: $e' : 'Failed to delete attachment: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Download attachment
  void _downloadAttachment(AttachmentModel attachment) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      // Show loading dialog with progress
      double progress = 0.0;
      bool isDownloading = true;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: Text(isRTL ? 'تحميل الملف' : 'Downloading File'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  attachment.originalFileName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                LinearProgressIndicator(value: progress),
                const SizedBox(height: 8),
                Text('${(progress * 100).toInt()}%'),
              ],
            ),
            actions: isDownloading
                ? []
                : [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(isRTL ? 'موافق' : 'OK'),
                    ),
                  ],
          ),
        ),
      );

      // Download file
      final filePath = await FileDownloadService.downloadFile(
        attachment: attachment,
        onProgress: (downloadProgress) {
          if (mounted) {
            setState(() {
              progress = downloadProgress;
            });
          }
        },
        openAfterDownload: true,
      );

      if (mounted) {
        setState(() {
          isDownloading = false;
        });

        Navigator.pop(context); // Close progress dialog

        if (filePath != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isRTL
                    ? 'تم تحميل الملف بنجاح: ${attachment.originalFileName}'
                    : 'File downloaded successfully: ${attachment.originalFileName}',
              ),
              backgroundColor: Colors.green,
              action: SnackBarAction(
                label: isRTL ? 'مشاركة' : 'Share',
                onPressed: () => _shareAttachment(attachment),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close progress dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                  ? 'فشل في تحميل الملف: $e'
                  : 'Failed to download file: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Share attachment
  void _shareAttachment(AttachmentModel attachment) async {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      await FileDownloadService.shareFile(attachment);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                  ? 'فشل في مشاركة الملف: $e'
                  : 'Failed to share file: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }


}

class _ImageViewerDialog extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;

  const _ImageViewerDialog({
    required this.imageUrls,
    required this.initialIndex,
  });

  @override
  State<_ImageViewerDialog> createState() => _ImageViewerDialogState();
}

class _ImageViewerDialogState extends State<_ImageViewerDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black,
      insetPadding: EdgeInsets.zero,
      child: Stack(
        children: [
          // Image viewer
          PageView.builder(
            controller: _pageController,
            itemCount: widget.imageUrls.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              return Center(
                child: InteractiveViewer(
                  child: Hero(
                    tag: 'image_${widget.imageUrls[index]}_$index',
                    child: CachedNetworkImage(
                      imageUrl: widget.imageUrls[index],
                      fit: BoxFit.contain,
                      httpHeaders: const {
                        'User-Agent': 'HMDeviceErrors/1.0',
                        'Accept': 'image/*',
                      },
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(color: Colors.white),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, color: Colors.white, size: 48),
                            SizedBox(height: 8),
                            Text(
                              'Failed to load image',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // Close button
          Positioned(
            top: 40,
            right: 20,
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white, size: 30),
              onPressed: () => Navigator.pop(context),
            ),
          ),

          // Image counter
          if (widget.imageUrls.length > 1)
            Positioned(
              top: 40,
              left: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${_currentIndex + 1} / ${widget.imageUrls.length}',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),

          // Navigation arrows
          if (widget.imageUrls.length > 1) ...[
            // Previous button
            if (_currentIndex > 0)
              Positioned(
                left: 20,
                top: 0,
                bottom: 0,
                child: Center(
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 30),
                    onPressed: () {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                  ),
                ),
              ),

            // Next button
            if (_currentIndex < widget.imageUrls.length - 1)
              Positioned(
                right: 20,
                top: 0,
                bottom: 0,
                child: Center(
                  child: IconButton(
                    icon: const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 30),
                    onPressed: () {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }
}

// Image Upload Dialog
class _ImageUploadDialog extends StatefulWidget {
  final DeviceError error;
  final Function(List<String>) onImagesUploaded;

  const _ImageUploadDialog({
    required this.error,
    required this.onImagesUploaded,
  });

  @override
  State<_ImageUploadDialog> createState() => _ImageUploadDialogState();
}

class _ImageUploadDialogState extends State<_ImageUploadDialog> {
  List<String> _newImageUrls = [];
  List<String> _originalImageUrls = [];
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _originalImageUrls = List.from(_newImageUrls);
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return PopScope(
      canPop: !_isUploading,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        if (_isUploading) {
          _showUploadInProgressDialog();
          return;
        }

        // Clean up uploaded images that weren't saved
        await _cleanupUnusedImages();
        if (mounted && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withAlpha(30),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.add_photo_alternate,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                isRTL ? 'إضافة صور جديدة' : 'Add New Images',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 450,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(100),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withAlpha(100),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isRTL
                          ? 'اختر الصور لإضافتها للعطل (الحد الأقصى 5 صور)'
                          : 'Select images to add to the error (max 5 images)',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ImageUploadWidget(
                  imageUrls: _newImageUrls,
                  onImagesChanged: (urls) {
                    setState(() {
                      _newImageUrls = urls;
                    });
                  },
                  onUploadStateChanged: (isUploading) {
                    setState(() {
                      _isUploading = isUploading;
                    });
                  },
                  maxImages: 5,
                  preferredService: 'google_drive',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: _isUploading ? null : () async {
              await _cleanupUnusedImages();
              if (mounted && context.mounted) {
                Navigator.pop(context);
              }
            },
            icon: const Icon(Icons.close, size: 18),
            label: Text(isRTL ? 'إلغاء' : 'Cancel'),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
          ),
          ElevatedButton.icon(
            onPressed: (_newImageUrls.isNotEmpty && !_isUploading)
                ? () {
                    widget.onImagesUploaded(_newImageUrls);
                    Navigator.pop(context);
                  }
                : null,
            icon: _isUploading
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  )
                : const Icon(Icons.add, size: 18),
            label: Text(
              _isUploading
                ? (isRTL ? 'جاري الرفع...' : 'Uploading...')
                : (isRTL ? 'إضافة' : 'Add')
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showUploadInProgressDialog() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.upload_file,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(isRTL ? 'جاري الرفع' : 'Upload in Progress'),
          ],
        ),
        content: Text(
          isRTL
            ? 'يتم رفع الصور حالياً. يرجى الانتظار حتى اكتمال العملية.'
            : 'Images are currently being uploaded. Please wait for the process to complete.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              isRTL ? 'فهمت' : 'Got it',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cleanupUnusedImages() async {
    // Find images that were uploaded but not saved
    final unusedImages = _newImageUrls.where((url) => !_originalImageUrls.contains(url)).toList();

    if (unusedImages.isNotEmpty) {
      try {
        // Delete unused images from cloud storage
        for (final imageUrl in unusedImages) {
          await CloudUploadService.deleteFile(imageUrl);
        }
        debugPrint('Cleaned up ${unusedImages.length} unused images');
      } catch (e) {
        debugPrint('Error cleaning up unused images: $e');
      }
    }
  }
}

// Image Edit Dialog
class _ImageEditDialog extends StatefulWidget {
  final DeviceError error;
  final Function(List<String>) onImagesUpdated;

  const _ImageEditDialog({
    required this.error,
    required this.onImagesUpdated,
  });

  @override
  State<_ImageEditDialog> createState() => _ImageEditDialogState();
}

class _ImageEditDialogState extends State<_ImageEditDialog> {
  late List<String> _imageUrls;

  @override
  void initState() {
    super.initState();
    _imageUrls = List.from(widget.error.imageUrls);
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return AlertDialog(
      title: Text(isRTL ? 'تعديل الصور' : 'Edit Images'),
      content: SizedBox(
        width: double.maxFinite,
        height: 500,
        child: Column(
          children: [
            Text(
              isRTL
                ? 'يمكنك حذف أو إضافة صور للعطل'
                : 'You can delete or add images to the error',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _imageUrls.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.image_not_supported,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            isRTL ? 'لا توجد صور' : 'No images',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                  : GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _imageUrls.length,
                      itemBuilder: (context, index) {
                        return Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedNetworkImage(
                                imageUrl: _imageUrls[index],
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: double.infinity,
                                placeholder: (context, url) => Container(
                                  color: Colors.grey[300],
                                  child: const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: Colors.grey[300],
                                  child: const Icon(Icons.error),
                                ),
                              ),
                            ),
                            Positioned(
                              top: 4,
                              right: 4,
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _imageUrls.removeAt(index);
                                  });
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddImageDialog(),
              icon: const Icon(Icons.add_photo_alternate),
              label: Text(isRTL ? 'إضافة صورة' : 'Add Image'),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(isRTL ? 'إلغاء' : 'Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onImagesUpdated(_imageUrls);
            Navigator.pop(context);
          },
          child: Text(isRTL ? 'حفظ' : 'Save'),
        ),
      ],
    );
  }

  void _showAddImageDialog() {
    showDialog(
      context: context,
      builder: (context) => _ImageUploadDialog(
        error: widget.error,
        onImagesUploaded: (newUrls) {
          setState(() {
            _imageUrls.addAll(newUrls);
          });
        },
      ),
    );
  }
}

// Attachment Picker Dialog with unsaved changes protection
class _AttachmentPickerDialog extends StatefulWidget {
  final DeviceError error;
  final Function(List<AttachmentModel>) onAttachmentsAdded;

  const _AttachmentPickerDialog({
    required this.error,
    required this.onAttachmentsAdded,
  });

  @override
  State<_AttachmentPickerDialog> createState() => _AttachmentPickerDialogState();
}

class _AttachmentPickerDialogState extends State<_AttachmentPickerDialog> {
  List<AttachmentModel> _newAttachments = [];
  List<AttachmentModel> _originalAttachments = [];
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _originalAttachments = List.from(_newAttachments);
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return PopScope(
      canPop: !_isUploading,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        if (_isUploading) {
          _showUploadInProgressDialog();
          return;
        }

        // Always cleanup unused attachments when canceling
        await _cleanupUnusedAttachments();
        if (mounted && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.add_photo_alternate, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            Text(isRTL ? 'إضافة مرفقات' : 'Add Attachments'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              if (_isUploading)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.orange.shade600,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          isRTL ? 'جاري رفع المرفقات...' : 'Uploading attachments...',
                          style: TextStyle(color: Colors.orange.shade800),
                        ),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: 16),
              Expanded(
                child: AttachmentPickerWidget(
                  initialAttachments: _newAttachments,
                  onAttachmentsChanged: (attachments) {
                    setState(() {
                      _newAttachments = attachments;
                    });
                  },
                  onUploadStateChanged: (isUploading) {
                    setState(() {
                      _isUploading = isUploading;
                    });
                  },
                  maxAttachments: 10,
                  allowImages: true,
                  allowVideos: true,
                  allowDocuments: true,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: _isUploading ? null : () async {
              await _cleanupUnusedAttachments();
              if (mounted && context.mounted) {
                Navigator.pop(context);
              }
            },
            icon: const Icon(Icons.close, size: 18),
            label: Text(isRTL ? 'إلغاء' : 'Cancel'),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
          ),
          ElevatedButton.icon(
            onPressed: (_isUploading || _newAttachments.isEmpty) ? null : () {
              widget.onAttachmentsAdded(_newAttachments);
              Navigator.pop(context);
            },
            icon: const Icon(Icons.save, size: 18),
            label: Text(isRTL ? 'حفظ' : 'Save'),
          ),
        ],
      ),
    );
  }

  void _showUploadInProgressDialog() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 12),
            Text(isRTL ? 'جاري الرفع' : 'Uploading'),
          ],
        ),
        content: Text(
          isRTL
              ? 'جاري رفع المرفقات، يرجى الانتظار...'
              : 'Uploading attachments, please wait...',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'فهمت' : 'Got it'),
          ),
        ],
      ),
    );
  }

  Future<void> _cleanupUnusedAttachments() async {
    // Find attachments that were uploaded but not saved
    final unusedAttachments = _newAttachments.where((attachment) =>
        !_originalAttachments.contains(attachment)).toList();

    for (final attachment in unusedAttachments) {
      try {
        await CloudUploadService.deleteFile(attachment.url);
        if (attachment.thumbnailUrl != null && attachment.thumbnailUrl!.isNotEmpty) {
          await CloudUploadService.deleteFile(attachment.thumbnailUrl!);
        }
      } catch (e) {
        debugPrint('Error cleaning up attachment: $e');
      }
    }
  }
}

// Attachment Edit Dialog
class _AttachmentEditDialog extends StatefulWidget {
  final DeviceError error;
  final Function(List<AttachmentModel>) onAttachmentsUpdated;

  const _AttachmentEditDialog({
    required this.error,
    required this.onAttachmentsUpdated,
  });

  @override
  State<_AttachmentEditDialog> createState() => _AttachmentEditDialogState();
}

class _AttachmentEditDialogState extends State<_AttachmentEditDialog> {
  late List<AttachmentModel> _attachments;

  @override
  void initState() {
    super.initState();
    _attachments = List.from(widget.error.attachments);
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(Icons.edit, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8),
          Text(isRTL ? 'تعديل المرفقات' : 'Edit Attachments'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            Text(
              isRTL
                  ? 'يمكنك إعادة ترتيب أو حذف المرفقات'
                  : 'You can reorder or delete attachments',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _attachments.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.attach_file, size: 48, color: Colors.grey.shade400),
                          const SizedBox(height: 8),
                          Text(
                            isRTL ? 'لا توجد مرفقات' : 'No attachments',
                            style: TextStyle(color: Colors.grey.shade600),
                          ),
                        ],
                      ),
                    )
                  : ReorderableListView.builder(
                      itemCount: _attachments.length,
                      onReorder: (oldIndex, newIndex) {
                        setState(() {
                          if (newIndex > oldIndex) newIndex--;
                          final attachment = _attachments.removeAt(oldIndex);
                          _attachments.insert(newIndex, attachment);
                        });
                      },
                      itemBuilder: (context, index) {
                        final attachment = _attachments[index];
                        return ListTile(
                          key: ValueKey(attachment.id),
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: _getTypeColor(attachment.type),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              _getTypeIcon(attachment.type),
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            attachment.originalFileName,
                            style: const TextStyle(fontSize: 14),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Text(
                            _formatFileSize(attachment.fileSize),
                            style: const TextStyle(fontSize: 12),
                          ),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                            onPressed: () => _deleteAttachment(index),
                          ),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showAddAttachmentDialog(),
              icon: const Icon(Icons.add, size: 18),
              label: Text(isRTL ? 'إضافة مرفق' : 'Add Attachment'),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(isRTL ? 'إلغاء' : 'Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onAttachmentsUpdated(_attachments);
            Navigator.pop(context);
          },
          child: Text(isRTL ? 'حفظ' : 'Save'),
        ),
      ],
    );
  }

  void _deleteAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  void _showAddAttachmentDialog() {
    showDialog(
      context: context,
      builder: (context) => _AttachmentPickerDialog(
        error: widget.error,
        onAttachmentsAdded: (newAttachments) {
          setState(() {
            _attachments.addAll(newAttachments);
          });
        },
      ),
    );
  }

  Color _getTypeColor(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return Colors.green;
      case AttachmentType.video:
        return Colors.blue;
      case AttachmentType.document:
        return Colors.orange;
    }
  }

  IconData _getTypeIcon(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return Icons.image;
      case AttachmentType.video:
        return Icons.videocam;
      case AttachmentType.document:
        return Icons.description;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}


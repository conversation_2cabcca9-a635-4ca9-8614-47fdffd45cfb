import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/device_error_model.dart';
import '../../models/attachment_model.dart';
import '../../providers/error_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/attachment_provider.dart';
import '../../widgets/gradient_background.dart';

import 'error_details_screen.dart';


class ErrorsListScreen extends StatefulWidget {
  const ErrorsListScreen({super.key});

  @override
  State<ErrorsListScreen> createState() => _ErrorsListScreenState();
}

class _ErrorsListScreenState extends State<ErrorsListScreen> {
  List<DeviceError> _filteredErrors = [];
  String _searchQuery = '';
  String _selectedCategory = '';
  String _selectedManufacturer = '';
  bool _isLoading = true;
  bool _isSelectionMode = false;
  final Set<String> _selectedErrorIds = <String>{};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

      // Load both errors and categories
      await Future.wait([
        errorProvider.loadErrors(),
        categoryProvider.fetchCategories(),
      ]);

      _applyFilters();

      // Preload attachments for visible errors
      _preloadAttachmentsForVisibleErrors();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadErrors() async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    await errorProvider.loadErrors();
    _applyFilters();
  }

  void _applyFilters() {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    List<DeviceError> errors = errorProvider.errors;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      errors = errors.where((error) {
        return error.errorCode.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               error.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               error.manufacturer.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               error.model.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply category filter
    if (_selectedCategory.isNotEmpty) {
      errors = errors.where((error) => error.categoryId == _selectedCategory).toList();
    }

    // Apply manufacturer filter
    if (_selectedManufacturer.isNotEmpty) {
      errors = errors.where((error) => error.manufacturer == _selectedManufacturer).toList();
    }

    setState(() {
      _filteredErrors = errors;
    });
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedErrorIds.clear();
      }
    });
  }

  void _toggleErrorSelection(String errorId) {
    setState(() {
      if (_selectedErrorIds.contains(errorId)) {
        _selectedErrorIds.remove(errorId);
      } else {
        _selectedErrorIds.add(errorId);
      }
    });
  }

  void _selectAllErrors() {
    setState(() {
      _selectedErrorIds.addAll(_filteredErrors.map((e) => e.id));
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedErrorIds.clear();
    });
  }

  void _showBulkActions() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final canEditErrors = authProvider.canEditErrors;
    final canDeleteErrors = authProvider.canDeleteErrors;

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              isRTL ? 'الإجراءات المتاحة' : 'Available Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            if (canEditErrors)
              ListTile(
                leading: const Icon(Icons.edit),
                title: Text(isRTL ? 'تعديل متعدد' : 'Bulk Edit'),
                onTap: () {
                  Navigator.pop(context);
                  _performBulkEdit();
                },
              ),
            if (canDeleteErrors)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: Text(
                  isRTL ? 'حذف متعدد' : 'Bulk Delete',
                  style: const TextStyle(color: Colors.red),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _showBulkDeleteConfirmation();
                },
              ),
            ListTile(
              leading: const Icon(Icons.favorite),
              title: Text(isRTL ? 'إضافة للمفضلة' : 'Add to Favorites'),
              onTap: () {
                Navigator.pop(context);
                _performBulkFavorite(true);
              },
            ),
            ListTile(
              leading: const Icon(Icons.favorite_border),
              title: Text(isRTL ? 'إزالة من المفضلة' : 'Remove from Favorites'),
              onTap: () {
                Navigator.pop(context);
                _performBulkFavorite(false);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _performBulkEdit() {
    // Implementation for bulk edit
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Bulk edit feature coming soon')),
    );
  }

  void _showBulkDeleteConfirmation() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تأكيد الحذف المتعدد' : 'Confirm Bulk Delete'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من حذف ${_selectedErrorIds.length} عطل؟'
              : 'Are you sure you want to delete ${_selectedErrorIds.length} errors?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performBulkDelete();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  void _performBulkDelete() async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      for (final errorId in _selectedErrorIds) {
        await errorProvider.deleteError(errorId);
      }

      if (mounted) {
        setState(() {
          _isSelectionMode = false;
          _selectedErrorIds.clear();
        });

        _loadErrors();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL ? 'تم حذف الأعطال بنجاح' : 'Errors deleted successfully',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _performBulkFavorite(bool isFavorite) async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    try {
      for (final errorId in _selectedErrorIds) {
        final error = _filteredErrors.firstWhere((e) => e.id == errorId);
        if (error.isFavorite != isFavorite) {
          await errorProvider.toggleFavorite(error);
        }
      }

      if (mounted) {
        setState(() {
          _isSelectionMode = false;
          _selectedErrorIds.clear();
        });

        _loadErrors();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isFavorite
                  ? (isRTL ? 'تمت إضافة الأعطال للمفضلة' : 'Errors added to favorites')
                  : (isRTL ? 'تمت إزالة الأعطال من المفضلة' : 'Errors removed from favorites'),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _preloadAttachmentsForVisibleErrors() async {
    if (!mounted) return;

    try {
      final attachmentProvider = Provider.of<AttachmentProvider>(context, listen: false);

      // Get first 10 errors (visible ones)
      final visibleErrors = _filteredErrors.take(10).toList();

      // Collect all attachments from visible errors
      final allAttachments = <AttachmentModel>[];
      for (final error in visibleErrors) {
        allAttachments.addAll(error.attachments);
      }

      if (allAttachments.isNotEmpty) {
        // Preload attachments with priority
        await attachmentProvider.preloadAttachments(allAttachments);
      }
    } catch (e) {
      debugPrint('Error preloading attachments: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final canEditErrors = authProvider.canEditErrors;
    final canDeleteErrors = authProvider.canDeleteErrors;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
/*       appBar: AppBar(
        title: Row(
          children: [
            AppLogo(
              size: 28,
              heroTag: 'errors_logo',
              onTap: () => LogoDisplayDialog.show(context),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                isRTL ? 'الأعطال' : 'Errors',
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadErrors,
            tooltip: isRTL ? 'تحديث' : 'Refresh',
          ),
        ],
      ), */
        body: Column(
          children: [
            // SafeArea for status bar
            SafeArea(
              bottom: false,
              child: Column(
                children: [
                  // Add some top padding for better spacing
                  const SizedBox(height: 8),

                  // Selection Mode Header
                  if (_isSelectionMode) _buildSelectionHeader(isRTL),

                  // Search and Filter Section
                  _buildSearchAndFilters(isRTL),
                ],
              ),
            ),

            // Errors List (outside SafeArea to use full height)
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredErrors.isEmpty
                      ? _buildEmptyState(isRTL)
                      : _buildErrorsList(isRTL, canEditErrors, canDeleteErrors),
            ),
          ],
        ),
      // Remove floating action button for regular users
      floatingActionButton: null,
      ),
    );
  }

  Widget _buildSelectionHeader(bool isRTL) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Text(
            isRTL
                ? '${_selectedErrorIds.length}'
                : '${_selectedErrorIds.length}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: _selectAllErrors,
            child: Text(
              isRTL ? 'تحديد الكل' : 'Select All',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          TextButton(
            onPressed: _clearSelection,
            child: Text(
              isRTL ? 'إلغاء التحديد' : 'Clear',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          IconButton(
            onPressed: _showBulkActions,
            icon: const Icon(Icons.more_vert),
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
          IconButton(
            onPressed: _toggleSelectionMode,
            icon: const Icon(Icons.close),
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(bool isRTL) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search Bar with Selection Button
          Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 48,
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: isRTL ? 'البحث في الأعطال...' : 'Search errors...',
                      prefixIcon: const Icon(Icons.search, size: 22),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withAlpha(100),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withAlpha(100),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(
                          color: Theme.of(context).colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: Theme.of(context).colorScheme.surface,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    style: const TextStyle(fontSize: 16),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                      _applyFilters();
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _toggleSelectionMode,
                icon: Icon(
                  _isSelectionMode ? Icons.check_box : Icons.check_box_outline_blank,
                  color: _isSelectionMode
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface.withAlpha(128),
                ),
                tooltip: isRTL ? 'وضع التحديد' : 'Selection Mode',
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Filter Chips
          Consumer<CategoryProvider>(
            builder: (context, categoryProvider, child) {
              return SizedBox(
                height: 32,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        label: isRTL ? 'الكل' : 'All',
                        isSelected: _selectedCategory.isEmpty && _selectedManufacturer.isEmpty,
                        onTap: () {
                          setState(() {
                            _selectedCategory = '';
                            _selectedManufacturer = '';
                          });
                          _applyFilters();
                        },
                      ),
                      const SizedBox(width: 6),
                      ...categoryProvider.categories.map((category) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 6),
                          child: _buildFilterChip(
                            label: category.name,
                            isSelected: _selectedCategory == category.id,
                            onTap: () {
                              setState(() {
                                _selectedCategory = _selectedCategory == category.id ? '' : category.id;
                                _selectedManufacturer = '';
                              });
                              _applyFilters();
                            },
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              );
            },
          ),

          // Add bottom padding for better spacing
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected
                ? Colors.white
                : Theme.of(context).colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isRTL) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(102),
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory.isNotEmpty || _selectedManufacturer.isNotEmpty
                ? (isRTL ? 'لا توجد نتائج للبحث' : 'No search results')
                : (isRTL ? 'لا توجد أعطال' : 'No errors found'),
            style: TextStyle(
              fontSize: 18,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'Try changing your search or filter criteria',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorsList(bool isRTL, bool canEditErrors, bool canDeleteErrors) {
    return RefreshIndicator(
      onRefresh: _loadErrors,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredErrors.length,
        itemBuilder: (context, index) {
          final error = _filteredErrors[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildErrorCard(error, isRTL, canEditErrors, canDeleteErrors),
          );
        },
      ),
    );
  }

  Widget _buildErrorCard(DeviceError error, bool isRTL, bool canEditErrors, bool canDeleteErrors) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isSelected = _selectedErrorIds.contains(error.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : (isDark
                  ? Theme.of(context).colorScheme.outline.withAlpha(120)
                  : const Color(0xFF2563EB).withAlpha(100)), // حدود زرقاء فاتحة
          width: isSelected ? 2.0 : 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: isSelected
            ? Theme.of(context).colorScheme.primaryContainer.withAlpha(100)
            : (isDark
                ? Theme.of(context).colorScheme.surface
                : const Color(0xFFF8FAFF)), // خلفية زرقاء فاتحة جداً
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: InkWell(
          onTap: () {
            if (_isSelectionMode) {
              _toggleErrorSelection(error.id);
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ErrorDetailsScreen(errorId: error.id),
                ),
              );
            }
          },
          onLongPress: () {
            if (!_isSelectionMode) {
              _toggleSelectionMode();
              _toggleErrorSelection(error.id);
            }
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isSelected
                    ? [
                        Theme.of(context).colorScheme.primaryContainer.withAlpha(150),
                        Theme.of(context).colorScheme.primaryContainer.withAlpha(100),
                      ]
                    : (isDark ? [
                        Theme.of(context).colorScheme.surface,
                        Theme.of(context).colorScheme.surface.withAlpha(230),
                      ] : [
                        const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                        const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                        const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
                      ]),
                stops: isDark || isSelected ? null : [0.0, 0.5, 1.0],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                // Header with title and selection indicator
                Row(
                  children: [
                    // Selection indicator
                    if (_isSelectionMode) ...[
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.grey.shade300,
                          border: Border.all(
                            color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey.shade400,
                            width: 2,
                          ),
                        ),
                        child: isSelected
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                      ),
                      const SizedBox(width: 12),
                    ],
                    Expanded(
                      child: Text(
                        '${error.manufacturer} ${error.model} - ${error.errorCode}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Enhanced favorite button (always visible)
                    if (!_isSelectionMode)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        decoration: BoxDecoration(
                          gradient: error.isFavorite
                              ? LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.red.withAlpha(240),
                                    Colors.red.shade600.withAlpha(220),
                                  ],
                                )
                              : null,
                          color: error.isFavorite
                              ? null
                              : Theme.of(context).colorScheme.surface.withAlpha(100),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: error.isFavorite
                                ? Colors.white.withAlpha(100)
                                : Theme.of(context).colorScheme.outline.withAlpha(80),
                            width: 1.5,
                          ),
                          boxShadow: error.isFavorite
                              ? [
                                  BoxShadow(
                                    color: Colors.red.withAlpha(60),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                    spreadRadius: 1,
                                  ),
                                ]
                              : [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(20),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(20),
                            onTap: () => _toggleFavorite(error),
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                error.isFavorite ? Icons.favorite : Icons.favorite_border,
                                color: error.isFavorite
                                    ? Colors.white
                                    : Theme.of(context).colorScheme.onSurface.withAlpha(160),
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 8),

                // Device info
                Row(
                  children: [
                    Icon(
                      Icons.devices,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${error.manufacturer} - ${error.model}',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Description
                Text(
                  error.description,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface.withAlpha(178),
                    fontSize: 14,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // Tags and info
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                      decoration: BoxDecoration(
                        color: isDark
                            ? Theme.of(context).colorScheme.primary.withAlpha(25)
                            : const Color(0xFF2563EB).withAlpha(30), // زرقاء فاتحة
                        borderRadius: BorderRadius.circular(14),
                        border: Border.all(
                          color: isDark
                              ? Theme.of(context).colorScheme.primary.withAlpha(60)
                              : const Color(0xFF2563EB).withAlpha(50),
                          width: 1,
                        ),
                      ),
                      child: Consumer<CategoryProvider>(
                        builder: (context, categoryProvider, child) {
                          final category = categoryProvider.getCategoryById(error.categoryId);
                          return Text(
                            category?.name ?? error.categoryId,
                            style: TextStyle(
                              color: isDark
                                  ? Theme.of(context).colorScheme.primary
                                  : const Color(0xFF2563EB), // نص أزرق
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (error.imageUrls.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: isDark
                              ? Theme.of(context).colorScheme.tertiary.withAlpha(25)
                              : const Color(0xFF059669).withAlpha(30), // أخضر فاتح
                          borderRadius: BorderRadius.circular(14),
                          border: Border.all(
                            color: isDark
                                ? Theme.of(context).colorScheme.tertiary.withAlpha(60)
                                : const Color(0xFF059669).withAlpha(50),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.image,
                              size: 12,
                              color: isDark
                                  ? Theme.of(context).colorScheme.tertiary
                                  : const Color(0xFF059669), // أيقونة خضراء
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${error.imageUrls.length}',
                              style: TextStyle(
                                color: isDark
                                    ? Theme.of(context).colorScheme.tertiary
                                    : const Color(0xFF059669), // نص أخضر
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    const Spacer(),
                    Text(
                      _formatDate(error.createdAt),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _toggleFavorite(DeviceError error) async {
    final errorProvider = Provider.of<ErrorProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    if (authProvider.user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL
                ? 'يجب تسجيل الدخول لإضافة العطل إلى المفضلة'
                : 'You must be logged in to add to favorites',
          ),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    try {
      final success = await errorProvider.toggleFavorite(error);

      if (success && mounted) {
        // Reload errors to update UI
        _loadErrors();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              error.isFavorite
                  ? (isRTL ? 'تمت إزالة العطل من المفضلة' : 'Removed from favorites')
                  : (isRTL ? 'تمت إضافة العطل إلى المفضلة' : 'Added to favorites'),
            ),
            backgroundColor: Theme.of(context).colorScheme.tertiary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
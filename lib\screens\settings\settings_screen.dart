import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/locale_provider.dart';
import '../../providers/user_settings_provider.dart';
import '../../widgets/app_logo.dart';
import '../../utils/theme.dart';
import '../../widgets/gradient_background.dart';
import '../profile/user_profile_screen.dart';
import '../../widgets/app_version_widget.dart';
import '../cache_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize user settings
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final settingsProvider = Provider.of<UserSettingsProvider>(context, listen: false);

      if (authProvider.user != null) {
        settingsProvider.initializeSettings(authProvider.user!.id);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final settingsProvider = Provider.of<UserSettingsProvider>(context);
    final user = authProvider.user;
    final isRTL = localeProvider.isRTL;

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: ListView(
          padding: const EdgeInsets.symmetric(vertical: 8),
          children: [
          // User profile section
          if (user != null)
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: themeProvider.isDarkMode
                      ? [
                          const Color(0xFF1E293B).withAlpha(240),
                          const Color(0xFF334155).withAlpha(220),
                        ]
                      : [
                          Colors.white.withAlpha(250),
                          const Color(0xFFF8FAFF).withAlpha(240),
                        ],
                ),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withAlpha(100),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: themeProvider.isDarkMode
                        ? Colors.black.withAlpha(150)
                        : Colors.grey.withAlpha(100),
                    blurRadius: 16,
                    offset: const Offset(0, 6),
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withAlpha(30),
                    blurRadius: 24,
                    offset: const Offset(0, 12),
                    spreadRadius: -4,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(28),
                child: Column(
                  children: [
                    // Profile Image with enhanced design
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.primary.withAlpha(200),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.primary.withAlpha(100),
                            blurRadius: 20,
                            spreadRadius: 4,
                            offset: const Offset(0, 8),
                          ),
                          BoxShadow(
                            color: Colors.black.withAlpha(40),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                        ),
                        child: user.profileImageUrl != null && user.profileImageUrl!.isNotEmpty
                            ? CircleAvatar(
                                radius: 56,
                                backgroundImage: NetworkImage(user.profileImageUrl!),
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                onBackgroundImageError: (exception, stackTrace) {
                                  // Fallback to initials if image fails to load
                                },
                                child: user.profileImageUrl!.isEmpty
                                    ? Text(
                                        user.displayName?.substring(0, 1).toUpperCase() ?? 'U',
                                        style: const TextStyle(
                                          fontSize: 40,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : null,
                              )
                            : CircleAvatar(
                                radius: 56,
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                child: Text(
                                  user.displayName?.substring(0, 1).toUpperCase() ?? 'U',
                                  style: const TextStyle(
                                    fontSize: 40,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // User Name
                    Text(
                      user.displayName ?? (isRTL ? 'مستخدم' : 'User'),
                      style: TextStyle(
                        fontSize: 26,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 6),

                    // Email
                    Text(
                      user.email,
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),

                    // Role Badge
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: user.role.toString().split('.').last == 'admin'
                              ? [
                                  Colors.red.withAlpha(200),
                                  Colors.red.shade600.withAlpha(180),
                                ]
                              : [
                                  Colors.blue.withAlpha(200),
                                  Colors.blue.shade600.withAlpha(180),
                                ],
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: (user.role.toString().split('.').last == 'admin'
                                    ? Colors.red
                                    : Colors.blue)
                                .withAlpha(60),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            user.role.toString().split('.').last == 'admin'
                                ? Icons.admin_panel_settings
                                : Icons.person,
                            color: Colors.white,
                            size: 18,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            user.role.toString().split('.').last == 'admin'
                                ? (isRTL ? 'مدير' : 'Admin')
                                : (isRTL ? 'مستخدم' : 'User'),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // App Appearance Settings
          _buildSectionHeader(context, isRTL ? 'مظهر التطبيق' : 'App Appearance'),

          _buildSettingCard(
            context,
            child: Column(
              children: [
                SwitchListTile(
                  title: Text(isRTL ? 'الوضع المظلم' : 'Dark Mode'),
                  subtitle: Text(isRTL ? 'تفعيل الوضع المظلم للتطبيق' : 'Enable dark theme for the app'),
                  value: themeProvider.isDarkMode,
                  onChanged: (value) async {
                    themeProvider.toggleTheme();
                    if (settingsProvider.settings != null) {
                      await settingsProvider.updateTheme(value ? 'dark' : 'light');
                    }
                  },
                  secondary: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.primary.withAlpha(100),
                          Theme.of(context).colorScheme.primary.withAlpha(60),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withAlpha(60),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      themeProvider.isDarkMode ? Icons.dark_mode_rounded : Icons.light_mode_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: Text(isRTL ? 'اللغة الإنجليزية' : 'English Language'),
                  subtitle: Text(
                    localeProvider.locale.languageCode == 'ar'
                        ? (isRTL ? 'التبديل إلى الإنجليزية' : 'Switch to English')
                        : (isRTL ? 'التبديل إلى العربية' : 'Switch to Arabic')
                  ),
                  value: localeProvider.locale.languageCode == 'en',
                  onChanged: (value) async {
                    localeProvider.toggleLocale();
                    if (settingsProvider.settings != null) {
                      await settingsProvider.updateLanguage(value ? 'en' : 'ar');
                    }
                  },
                  secondary: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.secondary.withAlpha(120),
                          Theme.of(context).colorScheme.secondary.withAlpha(80),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.secondary.withAlpha(60),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.language_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Backup Settings
          _buildSectionHeader(context, isRTL ? 'النسخ الاحتياطي' : 'Backup'),

          _buildSettingCard(
            context,
            child: SwitchListTile(
              title: Text(isRTL ? 'النسخ الاحتياطي التلقائي' : 'Auto Backup'),
              subtitle: Text(isRTL ? 'نسخ احتياطي تلقائي للصور' : 'Automatic backup of images'),
              value: settingsProvider.settings?.autoBackup ?? true,
              onChanged: (value) async {
                await settingsProvider.updateAutoBackup(value);
              },
              secondary: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.orange.withAlpha(120),
                      Colors.orange.shade600.withAlpha(100),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withAlpha(60),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.backup_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),

          // Notifications Settings
          _buildSectionHeader(context, isRTL ? 'الإشعارات' : 'Notifications'),

          _buildSettingCard(
            context,
            child: SwitchListTile(
              title: Text(isRTL ? 'تفعيل الإشعارات' : 'Enable Notifications'),
              subtitle: Text(isRTL ? 'استقبال إشعارات التطبيق' : 'Receive app notifications'),
              value: settingsProvider.settings?.enableNotifications ?? true,
              onChanged: (value) async {
                await settingsProvider.updateNotifications(value);
              },
              secondary: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.purple.withAlpha(120),
                      Colors.purple.shade600.withAlpha(100),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.purple.withAlpha(60),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.notifications_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),

          // Cache Settings
          _buildSectionHeader(context, isRTL ? 'التخزين المؤقت' : 'Cache'),

          _buildSettingCard(
            context,
            child: Column(
              children: [
                SwitchListTile(
                  title: Text(isRTL ? 'تفعيل التخزين المؤقت' : 'Enable Cache'),
                  subtitle: Text(isRTL ? 'حفظ الملفات مؤقتاً لتسريع الوصول' : 'Save files temporarily for faster access'),
                  value: settingsProvider.settings?.enableCache ?? true,
                  onChanged: (value) async {
                    await settingsProvider.updateCacheEnabled(value);
                  },
                  secondary: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.teal.withAlpha(120),
                          Colors.teal.shade600.withAlpha(100),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.teal.withAlpha(60),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.storage_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                if (settingsProvider.settings?.enableCache == true) ...[
                  const Divider(),
                  SwitchListTile(
                    title: Text(isRTL ? 'التنظيف التلقائي' : 'Auto Cleanup'),
                    subtitle: Text(isRTL ? 'حذف الملفات القديمة تلقائياً' : 'Automatically delete old files'),
                    value: settingsProvider.settings?.autoCleanupCache ?? true,
                    onChanged: (value) async {
                      await settingsProvider.updateAutoCleanupCache(value);
                    },
                    secondary: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.orange.withAlpha(120),
                            Colors.orange.shade600.withAlpha(100),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withAlpha(60),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.auto_delete_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const Divider(),
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.blue.withAlpha(120),
                            Colors.blue.shade600.withAlpha(100),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withAlpha(60),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.settings_rounded,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    title: Text(isRTL ? 'إعدادات متقدمة' : 'Advanced Settings'),
                    subtitle: Text(isRTL ? 'إدارة تفصيلية للتخزين المؤقت' : 'Detailed cache management'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _showAdvancedCacheSettings(context, settingsProvider, isRTL),
                  ),
                ],
              ],
            ),
          ),

          // User Profile
          _buildSectionHeader(context, isRTL ? 'الملف الشخصي' : 'Profile'),

          _buildSettingCard(
            context,
            child: ListTile(
              leading: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.blue.withAlpha(120),
                      Colors.blue.shade600.withAlpha(100),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withAlpha(60),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.person_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              title: Text(isRTL ? 'تعديل الملف الشخصي' : 'Edit Profile'),
              subtitle: Text(isRTL ? 'تعديل البيانات الشخصية وكلمة المرور' : 'Edit personal information and password'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const UserProfileScreen(),
                  ),
                );
              },
            ),
          ),

          // App Info & Actions
          _buildSectionHeader(context, isRTL ? 'معلومات التطبيق' : 'App Info'),

          _buildSettingCard(
            context,
            child: Column(
              children: [
                ListTile(
                  title: Text(isRTL ? 'حول التطبيق' : 'About App'),
                  subtitle: Text(isRTL ? 'معلومات حول التطبيق والإصدار' : 'App information and version'),
                  leading: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.blue.withAlpha(120),
                          Colors.blue.shade600.withAlpha(100),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withAlpha(60),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.info_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  trailing: Icon(
                    isRTL ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
                    size: 16,
                  ),
                  onTap: () => _showAboutDialog(context),
                ),
                const Divider(height: 1),
                ListTile(
                  title: Text(isRTL ? 'إعادة تعيين الإعدادات' : 'Reset Settings'),
                  subtitle: Text(isRTL ? 'إعادة الإعدادات إلى الافتراضية' : 'Reset all settings to default'),
                  leading: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.orange.withAlpha(120),
                          Colors.orange.shade600.withAlpha(100),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.withAlpha(60),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.refresh_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  trailing: Icon(
                    isRTL ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
                    size: 16,
                  ),
                  onTap: () => _showResetDialog(context),
                ),
                const Divider(height: 1),
                ListTile(
                  title: Text(
                    isRTL ? 'تسجيل الخروج' : 'Logout',
                    style: const TextStyle(color: Colors.red),
                  ),
                  subtitle: Text(isRTL ? 'الخروج من التطبيق' : 'Sign out from the app'),
                  leading: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.red.withAlpha(120),
                          Colors.red.shade600.withAlpha(100),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withAlpha(60),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.logout_rounded,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  onTap: () => _showLogoutDialog(context),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 28, 20, 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Theme.of(context).colorScheme.primary.withAlpha(40),
            Theme.of(context).colorScheme.primary.withAlpha(20),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withAlpha(100),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(100)
                : Colors.grey.withAlpha(60),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingCard(BuildContext context, {required Widget child}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDarkMode
              ? [
                  const Color(0xFF1E293B).withAlpha(240),
                  const Color(0xFF334155).withAlpha(200),
                ]
              : [
                  Colors.white.withAlpha(250),
                  const Color(0xFFF8FAFF).withAlpha(240),
                ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(80),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withAlpha(120)
                : Colors.grey.withAlpha(80),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withAlpha(20),
            blurRadius: 16,
            offset: const Offset(0, 8),
            spreadRadius: -2,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Theme.of(context).colorScheme.primary.withAlpha(8),
              ],
            ),
          ),
          child: child,
        ),
      ),
    );
  }



  void _showResetDialog(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final settingsProvider = Provider.of<UserSettingsProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    AppTheme.showBlurredDialog(
      context: context,
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(isRTL ? 'إعادة تعيين الإعدادات' : 'Reset Settings'),
        content: Text(
          isRTL
            ? 'هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'
            : 'Are you sure you want to reset all settings to default values?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              await settingsProvider.resetToDefaults();
              navigator.pop();
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text(
                    isRTL ? 'تم إعادة تعيين الإعدادات بنجاح' : 'Settings reset successfully',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: Text(isRTL ? 'إعادة تعيين' : 'Reset'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    AppTheme.showBlurredDialog(
      context: context,
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Text(isRTL ? 'تسجيل الخروج' : 'Logout'),
        content: Text(
          isRTL ? 'هل أنت متأكد من تسجيل الخروج؟' : 'Are you sure you want to logout?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final navigator = Navigator.of(context);
              Navigator.pop(context);
              authProvider.signOut().then((_) {
                if (mounted) {
                  navigator.pushReplacementNamed('/login');
                }
              });
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'تسجيل الخروج' : 'Logout'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    AppTheme.showBlurredDialog(
      context: context,
      child: AboutDialog(
        applicationName: isRTL ? 'أعطال الأجهزة المنزلية' : 'HM Device Errors',
        applicationVersion: '1.0.4', // Will be overridden by AppVersionInfo
        applicationIcon: const AppLogo(
          size: 64,
          padding: EdgeInsets.all(8),
          backgroundColor: Colors.transparent,
          borderRadius: 12,
        ),
        applicationLegalese: '© 2025 Mohamed Rady',
        children: [
          const SizedBox(height: 16),
          Text(
            isRTL
              ? 'تطبيق احترافي لإدارة أعطال الأجهزة المنزلية مع دعم تخزين الصور على الخدمات السحابية'
              : 'Professional app for managing home device errors with cloud storage support for images',
            textAlign: isRTL ? TextAlign.right : TextAlign.left,
          ),
          const SizedBox(height: 16),
          AppVersionInfo(isRTL: isRTL, showDetails: true),
        ],
      ),
    );
  }

  void _showAdvancedCacheSettings(BuildContext context, UserSettingsProvider settingsProvider, bool isRTL) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CacheSettingsScreen(),
      ),
    );
  }
}

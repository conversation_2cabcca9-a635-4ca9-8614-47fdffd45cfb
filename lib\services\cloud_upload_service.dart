import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/attachment_model.dart';
import 'google_drive_service.dart';

/// Service for uploading files to cloud storage with progress tracking
class CloudUploadService {
  static final GoogleDriveService _googleDriveService = GoogleDriveService();

  /// Upload file to cloud storage with progress callback
  static Future<String> uploadFile({
    required AttachmentModel attachment,
    required Uint8List fileData,
    required Function(double progress) onProgress,
    String? folderId,
  }) async {
    try {
      // Validate file data
      if (fileData.isEmpty) {
        throw Exception('File data is empty');
      }

      // Start upload progress
      onProgress(0.1);

      // Check if Google Drive service is available
      final isGoogleDriveConfigured = await _googleDriveService.isConfigured();

      if (!isGoogleDriveConfigured) {
        throw Exception('Google Drive service is not configured');
      }

      onProgress(0.2);

      // Upload to Google Drive with progress tracking
      final uploadedUrl = await _uploadToGoogleDriveWithProgress(
        attachment: attachment,
        fileData: fileData,
        onProgress: (progress) {
          // Map progress from 0.2 to 0.9 (leaving 0.1 for finalization)
          final mappedProgress = 0.2 + (progress * 0.7);
          onProgress(mappedProgress);
        },
        folderId: folderId,
      );

      onProgress(0.95);

      // Verify upload success
      if (uploadedUrl.isEmpty) {
        throw Exception('Upload failed: No URL returned');
      }

      // Final progress
      onProgress(1.0);

      debugPrint('File uploaded successfully: ${attachment.originalFileName}');
      return uploadedUrl;

    } catch (e) {
      debugPrint('Error uploading file ${attachment.originalFileName}: $e');
      rethrow;
    }
  }

  /// Upload to Google Drive with detailed progress tracking
  static Future<String> _uploadToGoogleDriveWithProgress({
    required AttachmentModel attachment,
    required Uint8List fileData,
    required Function(double progress) onProgress,
    String? folderId,
  }) async {
    try {
      // Initialize Google Drive service
      onProgress(0.1);

      // Check if service is configured first
      final isConfigured = await _googleDriveService.isConfigured();
      if (!isConfigured) {
        throw Exception('Google Drive service is not properly configured. Please check your settings.');
      }

      await _googleDriveService.initialize();
      onProgress(0.2);

      // Create file metadata
      final fileName = attachment.fileName;
      final mimeType = _getMimeType(attachment);

      onProgress(0.3);

      // Add retry logic for network issues
      String? uploadedUrl;
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          // Upload file with progress tracking
          uploadedUrl = await _googleDriveService.uploadFileWithProgress(
            fileName: fileName,
            fileData: fileData,
            mimeType: mimeType,
            folderId: folderId,
            onProgress: (progress) {
              // Map progress from 0.3 to 1.0
              final mappedProgress = 0.3 + (progress * 0.7);
              onProgress(mappedProgress);
            },
          );
          break; // Success, exit retry loop
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            rethrow; // Max retries reached, throw the error
          }

          debugPrint('Upload attempt $retryCount failed, retrying... Error: $e');
          // Wait before retry
          await Future.delayed(Duration(seconds: retryCount * 2));
          onProgress(0.2); // Reset progress for retry
        }
      }

      if (uploadedUrl == null || uploadedUrl.isEmpty) {
        throw Exception('Upload failed: No URL returned from Google Drive');
      }

      return uploadedUrl;

    } catch (e) {
      debugPrint('Error uploading to Google Drive: $e');
      rethrow;
    }
  }

  /// Get MIME type for attachment
  static String _getMimeType(AttachmentModel attachment) {
    switch (attachment.type) {
      case AttachmentType.image:
        final extension = attachment.fileExtension.toLowerCase();
        switch (extension) {
          case 'jpg':
          case 'jpeg':
            return 'image/jpeg';
          case 'png':
            return 'image/png';
          case 'gif':
            return 'image/gif';
          case 'webp':
            return 'image/webp';
          case 'bmp':
            return 'image/bmp';
          default:
            return 'image/jpeg';
        }

      case AttachmentType.video:
        final extension = attachment.fileExtension.toLowerCase();
        switch (extension) {
          case 'mp4':
            return 'video/mp4';
          case 'mov':
            return 'video/quicktime';
          case 'avi':
            return 'video/x-msvideo';
          case 'mkv':
            return 'video/x-matroska';
          case 'webm':
            return 'video/webm';
          case '3gp':
            return 'video/3gpp';
          default:
            return 'video/mp4';
        }

      case AttachmentType.document:
        final extension = attachment.fileExtension.toLowerCase();
        switch (extension) {
          case 'pdf':
            return 'application/pdf';
          case 'doc':
            return 'application/msword';
          case 'docx':
            return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          case 'txt':
            return 'text/plain';
          case 'rtf':
            return 'application/rtf';
          case 'odt':
            return 'application/vnd.oasis.opendocument.text';
          default:
            return 'application/octet-stream';
        }
    }
  }

  /// Delete file from cloud storage
  static Future<bool> deleteFile(String fileUrl) async {
    try {
      // Extract file ID from URL if it's a Google Drive URL
      if (fileUrl.contains('drive.google.com') || fileUrl.contains('googleapis.com')) {
        final fileId = _extractGoogleDriveFileId(fileUrl);
        if (fileId != null) {
          await _googleDriveService.initialize();
          return await _googleDriveService.deleteFile(fileId);
        }
      }

      // For other storage services, implement deletion logic here
      debugPrint('File deletion not implemented for URL: $fileUrl');
      return false;

    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  /// Extract Google Drive file ID from URL
  static String? _extractGoogleDriveFileId(String url) {
    try {
      // Handle different Google Drive URL formats
      final patterns = [
        RegExp(r'/file/d/([a-zA-Z0-9-_]+)'),
        RegExp(r'id=([a-zA-Z0-9-_]+)'),
        RegExp(r'/([a-zA-Z0-9-_]+)/view'),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(url);
        if (match != null && match.groupCount > 0) {
          return match.group(1);
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error extracting file ID from URL: $e');
      return null;
    }
  }

  /// Check if cloud storage is available
  static Future<bool> isCloudStorageAvailable() async {
    try {
      return await _googleDriveService.isConfigured();
    } catch (e) {
      debugPrint('Error checking cloud storage availability: $e');
      return false;
    }
  }

  /// Get upload progress status
  static String getProgressStatus(double progress) {
    if (progress < 0.2) {
      return 'جاري التحضير...';
    } else if (progress < 0.5) {
      return 'جاري الرفع...';
    } else if (progress < 0.9) {
      return 'جاري المعالجة...';
    } else if (progress < 1.0) {
      return 'جاري الإنهاء...';
    } else {
      return 'تم الرفع بنجاح';
    }
  }

  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Validate file before upload
  static bool validateFileForUpload(AttachmentModel attachment, Uint8List fileData) {
    // Check file size
    if (fileData.isEmpty) {
      return false;
    }

    // Check file size limits
    final maxSize = _getMaxFileSizeForType(attachment.type);
    if (fileData.length > maxSize) {
      return false;
    }

    // Check file extension
    final allowedExtensions = _getAllowedExtensionsForType(attachment.type);
    if (!allowedExtensions.contains(attachment.fileExtension.toLowerCase())) {
      return false;
    }

    return true;
  }

  /// Get maximum file size for type
  static int _getMaxFileSizeForType(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return 10 * 1024 * 1024; // 10MB
      case AttachmentType.video:
        return 50 * 1024 * 1024; // 50MB
      case AttachmentType.document:
        return 25 * 1024 * 1024; // 25MB
    }
  }

  /// Get allowed extensions for type
  static List<String> _getAllowedExtensionsForType(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
      case AttachmentType.video:
        return ['mp4', 'mov', 'avi', 'mkv', 'webm', '3gp'];
      case AttachmentType.document:
        return ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'];
    }
  }
}

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart' show debugPrint, defaultTargetPlatform, TargetPlatform;
import '../firebase_options.dart';
import 'auth_service.dart';

class FirebaseService {
  static Future<void> initialize() async {
    try {
      // Add special handling for Windows platform
      if (defaultTargetPlatform == TargetPlatform.windows) {
        // Set SSL/TLS configuration for Windows
        debugPrint('Initializing Firebase for Windows platform');
      }

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      debugPrint('Firebase initialized successfully');

      // Check and create default admin user if needed
      try {
        final authService = AuthService();
        await authService.checkAndCreateDefaultAdmin();
      } catch (authError) {
        // Don't let auth initialization errors prevent the app from starting
        debugPrint('Warning: Error in admin user initialization: $authError');
      }
    } catch (e) {
      debugPrint('Error initializing Firebase: $e');
      // Don't rethrow to allow the app to continue even if Firebase init fails
      debugPrint('Continuing without Firebase initialization');
    }
  }
}

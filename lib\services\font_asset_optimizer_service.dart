import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تحسين الخطوط والأصول
class FontAssetOptimizerService {
  static FontAssetOptimizerService? _instance;
  static FontAssetOptimizerService get instance => _instance ??= FontAssetOptimizerService._();

  FontAssetOptimizerService._();

  static const String _fontAnalysisKey = 'font_analysis_data';
  static const String _assetAnalysisKey = 'asset_analysis_data';

  /// تحليل الخطوط المستخدمة
  Future<FontAnalysis> analyzeFonts() async {
    debugPrint('Starting font analysis...');

    final analysis = FontAnalysis();

    try {
      // تحليل خطوط AwanZaman
      analysis.customFonts = await _analyzeCustomFonts();

      // تحليل الخطوط النظام
      analysis.systemFonts = _analyzeSystemFonts();

      // تحليل أيقونات الخطوط
      analysis.iconFonts = await _analyzeIconFonts();

      // حساب الأحجام الإجمالية
      analysis.calculateTotalSizes();

      // إنشاء اقتراحات التحسين
      analysis.optimizationSuggestions = _generateFontOptimizations(analysis);

      // حفظ نتائج التحليل
      await _saveFontAnalysis(analysis);

      debugPrint('Font analysis completed');

    } catch (e) {
      debugPrint('Error analyzing fonts: $e');
    }

    return analysis;
  }

  /// تحليل الخطوط المخصصة
  Future<List<FontInfo>> _analyzeCustomFonts() async {
    final fonts = <FontInfo>[];

    try {
      // خطوط AwanZaman
      final awanZamanFonts = [
        'assets/fonts/AwanZaman-Regular.ttf',
        'assets/fonts/AwanZaman-Bold.ttf',
        'assets/fonts/AwanZaman-Light.ttf',
        'assets/fonts/AwanZaman-Medium.ttf',
      ];

      for (final fontPath in awanZamanFonts) {
        try {
          final fontData = await rootBundle.load(fontPath);
          final fontName = fontPath.split('/').last.replaceAll('.ttf', '');

          fonts.add(FontInfo(
            name: fontName,
            path: fontPath,
            size: fontData.lengthInBytes,
            type: FontType.custom,
            isEssential: _isFontEssential(fontName),
            usage: _estimateFontUsage(fontName),
          ));
        } catch (e) {
          debugPrint('Error loading font $fontPath: $e');
        }
      }
    } catch (e) {
      debugPrint('Error analyzing custom fonts: $e');
    }

    return fonts;
  }

  /// تحليل خطوط النظام
  List<FontInfo> _analyzeSystemFonts() {
    return [
      FontInfo(
        name: 'Roboto',
        path: 'system',
        size: 0, // خطوط النظام لا تحسب في حجم التطبيق
        type: FontType.system,
        isEssential: true,
        usage: FontUsage.high,
      ),
      FontInfo(
        name: 'Material Icons',
        path: 'system',
        size: 0,
        type: FontType.system,
        isEssential: true,
        usage: FontUsage.high,
      ),
    ];
  }

  /// تحليل أيقونات الخطوط
  Future<List<FontInfo>> _analyzeIconFonts() async {
    final iconFonts = <FontInfo>[];

    try {
      // تحليل Cupertino Icons إذا كانت مستخدمة
      try {
        final cupertinoData = await rootBundle.load('packages/cupertino_icons/assets/CupertinoIcons.ttf');
        iconFonts.add(FontInfo(
          name: 'CupertinoIcons',
          path: 'packages/cupertino_icons/assets/CupertinoIcons.ttf',
          size: cupertinoData.lengthInBytes,
          type: FontType.icon,
          isEssential: false,
          usage: FontUsage.low,
          optimizationNote: 'يمكن استبدالها بأيقونات Material أو أيقونات مخصصة',
        ));
      } catch (e) {
        // Cupertino Icons غير موجودة أو غير مستخدمة
      }

    } catch (e) {
      debugPrint('Error analyzing icon fonts: $e');
    }

    return iconFonts;
  }

  /// تحديد ما إذا كان الخط أساسي
  bool _isFontEssential(String fontName) {
    // الخط العادي والعريض أساسيان
    return fontName.contains('Regular') || fontName.contains('Bold');
  }

  /// تقدير استخدام الخط
  FontUsage _estimateFontUsage(String fontName) {
    if (fontName.contains('Regular')) return FontUsage.high;
    if (fontName.contains('Bold')) return FontUsage.medium;
    if (fontName.contains('Medium')) return FontUsage.medium;
    return FontUsage.low; // Light وغيرها
  }

  /// إنشاء اقتراحات تحسين الخطوط
  List<FontOptimization> _generateFontOptimizations(FontAnalysis analysis) {
    final optimizations = <FontOptimization>[];

    // البحث عن الخطوط غير الأساسية
    final allFonts = [...analysis.customFonts, ...analysis.iconFonts];

    for (final font in allFonts) {
      if (!font.isEssential) {
        optimizations.add(FontOptimization(
          fontName: font.name,
          currentSize: font.size,
          optimizationType: 'Remove',
          description: font.optimizationNote ?? 'خط غير أساسي يمكن إزالته',
          estimatedSaving: font.size,
          priority: _calculateFontOptimizationPriority(font),
        ));
      } else if (font.usage == FontUsage.low) {
        optimizations.add(FontOptimization(
          fontName: font.name,
          currentSize: font.size,
          optimizationType: 'Subset',
          description: 'إنشاء مجموعة فرعية من الخط تحتوي على الأحرف المستخدمة فقط',
          estimatedSaving: (font.size * 0.3).round(), // توفير 30%
          priority: OptimizationPriority.medium,
        ));
      }
    }

    // اقتراحات عامة
    if (analysis.customFonts.length > 2) {
      optimizations.add(FontOptimization(
        fontName: 'AwanZaman Family',
        currentSize: analysis.totalCustomFontSize,
        optimizationType: 'Consolidate',
        description: 'استخدام خطين فقط (عادي وعريض) بدلاً من جميع الأوزان',
        estimatedSaving: analysis.customFonts
            .where((f) => !f.isEssential)
            .fold(0, (sum, f) => sum + f.size),
        priority: OptimizationPriority.high,
      ));
    }

    return optimizations;
  }

  /// حساب أولوية تحسين الخط
  OptimizationPriority _calculateFontOptimizationPriority(FontInfo font) {
    if (font.size > 500 * 1024) { // أكبر من 500KB
      return OptimizationPriority.high;
    } else if (font.size > 200 * 1024) { // أكبر من 200KB
      return OptimizationPriority.medium;
    } else {
      return OptimizationPriority.low;
    }
  }

  /// تحليل الأصول (الصور والأيقونات)
  Future<AssetAnalysis> analyzeAssets() async {
    debugPrint('Starting asset analysis...');

    final analysis = AssetAnalysis();

    try {
      // تحليل الصور
      analysis.images = await _analyzeImages();

      // تحليل الأيقونات
      analysis.icons = await _analyzeIcons();

      // حساب الأحجام الإجمالية
      analysis.calculateTotalSizes();

      // إنشاء اقتراحات التحسين
      analysis.optimizationSuggestions = _generateAssetOptimizations(analysis);

      // حفظ نتائج التحليل
      await _saveAssetAnalysis(analysis);

      debugPrint('Asset analysis completed');

    } catch (e) {
      debugPrint('Error analyzing assets: $e');
    }

    return analysis;
  }

  /// تحليل الصور
  Future<List<AssetInfo>> _analyzeImages() async {
    final images = <AssetInfo>[];

    try {
      // قائمة الصور المتوقعة
      final imagePaths = [
        'assets/images/logo.svg',
        'assets/images/placeholder.png',
      ];

      for (final imagePath in imagePaths) {
        try {
          final imageData = await rootBundle.load(imagePath);
          final imageName = imagePath.split('/').last;

          images.add(AssetInfo(
            name: imageName,
            path: imagePath,
            size: imageData.lengthInBytes,
            type: AssetType.image,
            format: _getImageFormat(imageName),
            isEssential: _isImageEssential(imageName),
          ));
        } catch (e) {
          debugPrint('Error loading image $imagePath: $e');
        }
      }
    } catch (e) {
      debugPrint('Error analyzing images: $e');
    }

    return images;
  }

  /// تحليل الأيقونات
  Future<List<AssetInfo>> _analyzeIcons() async {
    final icons = <AssetInfo>[];

    try {
      // البحث عن أيقونات في مجلد assets/icons
      // هذا مثال - في التطبيق الحقيقي ستحتاج لقراءة المجلد
      final iconPaths = [
        // سيتم إضافة الأيقونات هنا عند وجودها
      ];

      for (final iconPath in iconPaths) {
        try {
          final iconData = await rootBundle.load(iconPath);
          final iconName = iconPath.split('/').last;

          icons.add(AssetInfo(
            name: iconName,
            path: iconPath,
            size: iconData.lengthInBytes,
            type: AssetType.icon,
            format: _getImageFormat(iconName),
            isEssential: false,
          ));
        } catch (e) {
          debugPrint('Error loading icon $iconPath: $e');
        }
      }
    } catch (e) {
      debugPrint('Error analyzing icons: $e');
    }

    return icons;
  }

  /// تحديد تنسيق الصورة
  String _getImageFormat(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    return extension;
  }

  /// تحديد ما إذا كانت الصورة أساسية
  bool _isImageEssential(String imageName) {
    return imageName.contains('logo') || imageName.contains('placeholder');
  }

  /// إنشاء اقتراحات تحسين الأصول
  List<AssetOptimization> _generateAssetOptimizations(AssetAnalysis analysis) {
    final optimizations = <AssetOptimization>[];

    // تحسين الصور
    for (final image in analysis.images) {
      if (image.format != 'webp' && image.size > 100 * 1024) { // أكبر من 100KB
        optimizations.add(AssetOptimization(
          assetName: image.name,
          currentSize: image.size,
          optimizationType: 'Convert to WebP',
          description: 'تحويل الصورة إلى تنسيق WebP لتوفير المساحة',
          estimatedSaving: (image.size * 0.3).round(), // توفير 30%
          priority: OptimizationPriority.high,
        ));
      }

      if (image.size > 500 * 1024) { // أكبر من 500KB
        optimizations.add(AssetOptimization(
          assetName: image.name,
          currentSize: image.size,
          optimizationType: 'Compress',
          description: 'ضغط الصورة لتقليل الحجم',
          estimatedSaving: (image.size * 0.4).round(), // توفير 40%
          priority: OptimizationPriority.high,
        ));
      }
    }

    // تحسين الأيقونات
    for (final icon in analysis.icons) {
      if (!icon.isEssential) {
        optimizations.add(AssetOptimization(
          assetName: icon.name,
          currentSize: icon.size,
          optimizationType: 'Remove',
          description: 'أيقونة غير مستخدمة يمكن إزالتها',
          estimatedSaving: icon.size,
          priority: OptimizationPriority.medium,
        ));
      }
    }

    return optimizations;
  }

  /// حفظ تحليل الخطوط
  Future<void> _saveFontAnalysis(FontAnalysis analysis) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final analysisData = {
        'timestamp': DateTime.now().toIso8601String(),
        'totalFonts': analysis.totalFonts,
        'totalSize': analysis.totalSize,
        'optimizationCount': analysis.optimizationSuggestions.length,
      };

      await prefs.setString(_fontAnalysisKey, jsonEncode(analysisData));
    } catch (e) {
      debugPrint('Error saving font analysis: $e');
    }
  }

  /// حفظ تحليل الأصول
  Future<void> _saveAssetAnalysis(AssetAnalysis analysis) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final analysisData = {
        'timestamp': DateTime.now().toIso8601String(),
        'totalAssets': analysis.totalAssets,
        'totalSize': analysis.totalSize,
        'optimizationCount': analysis.optimizationSuggestions.length,
      };

      await prefs.setString(_assetAnalysisKey, jsonEncode(analysisData));
    } catch (e) {
      debugPrint('Error saving asset analysis: $e');
    }
  }

  /// الحصول على إحصائيات الخطوط
  Future<Map<String, dynamic>?> getFontStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsStr = prefs.getString(_fontAnalysisKey);

      if (statsStr != null) {
        return jsonDecode(statsStr) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting font stats: $e');
    }

    return null;
  }

  /// الحصول على إحصائيات الأصول
  Future<Map<String, dynamic>?> getAssetStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsStr = prefs.getString(_assetAnalysisKey);

      if (statsStr != null) {
        return jsonDecode(statsStr) as Map<String, dynamic>;
      }
    } catch (e) {
      debugPrint('Error getting asset stats: $e');
    }

    return null;
  }
}

/// معلومات الخط
class FontInfo {
  final String name;
  final String path;
  final int size;
  final FontType type;
  final bool isEssential;
  final FontUsage usage;
  final String? optimizationNote;

  FontInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.type,
    required this.isEssential,
    required this.usage,
    this.optimizationNote,
  });

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// تحليل الخطوط
class FontAnalysis {
  List<FontInfo> customFonts = [];
  List<FontInfo> systemFonts = [];
  List<FontInfo> iconFonts = [];
  List<FontOptimization> optimizationSuggestions = [];

  int get totalFonts => customFonts.length + systemFonts.length + iconFonts.length;
  int get totalSize => customFonts.fold(0, (sum, f) => sum + f.size) +
                      iconFonts.fold(0, (sum, f) => sum + f.size);
  int get totalCustomFontSize => customFonts.fold(0, (sum, f) => sum + f.size);

  void calculateTotalSizes() {
    // يتم حساب الأحجام تلقائياً من خلال الخصائص أعلاه
  }
}

/// معلومات الأصل
class AssetInfo {
  final String name;
  final String path;
  final int size;
  final AssetType type;
  final String format;
  final bool isEssential;

  AssetInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.type,
    required this.format,
    required this.isEssential,
  });

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// تحليل الأصول
class AssetAnalysis {
  List<AssetInfo> images = [];
  List<AssetInfo> icons = [];
  List<AssetOptimization> optimizationSuggestions = [];

  int get totalAssets => images.length + icons.length;
  int get totalSize => images.fold(0, (sum, a) => sum + a.size) +
                       icons.fold(0, (sum, a) => sum + a.size);

  void calculateTotalSizes() {
    // يتم حساب الأحجام تلقائياً من خلال الخصائص أعلاه
  }
}

/// تحسين الخط
class FontOptimization {
  final String fontName;
  final int currentSize;
  final String optimizationType;
  final String description;
  final int estimatedSaving;
  final OptimizationPriority priority;

  FontOptimization({
    required this.fontName,
    required this.currentSize,
    required this.optimizationType,
    required this.description,
    required this.estimatedSaving,
    required this.priority,
  });
}

/// تحسين الأصل
class AssetOptimization {
  final String assetName;
  final int currentSize;
  final String optimizationType;
  final String description;
  final int estimatedSaving;
  final OptimizationPriority priority;

  AssetOptimization({
    required this.assetName,
    required this.currentSize,
    required this.optimizationType,
    required this.description,
    required this.estimatedSaving,
    required this.priority,
  });
}

/// نوع الخط
enum FontType {
  custom,
  system,
  icon,
}

/// استخدام الخط
enum FontUsage {
  high,
  medium,
  low,
}

/// نوع الأصل
enum AssetType {
  image,
  icon,
  font,
}

/// أولوية التحسين
enum OptimizationPriority {
  high,
  medium,
  low,
}

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:googleapis/drive/v3.dart' as drive;
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'storage_config_service.dart';
import 'remote_config_service.dart';
import '../models/storage_config_model.dart';

class GoogleDriveService {
  static const List<String> _scopes = [drive.DriveApi.driveFileScope];

  drive.DriveApi? _driveApi;
  bool _isInitialized = false;
  StorageConfigModel? _config;
  final StorageConfigService _configService = StorageConfigService();

  // Singleton pattern
  static final GoogleDriveService _instance = GoogleDriveService._internal();
  factory GoogleDriveService() => _instance;
  GoogleDriveService._internal();

  // Initialize Google Drive API
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize config service first
      await _configService.initialize();

      // Ensure remote config is initialized
      if (!RemoteConfigService.isInitialized) {
        await RemoteConfigService.initialize();
      }

      // Check configuration priority: Remote Config is now highest priority
      final useRemoteConfig = RemoteConfigService.useRemoteConfigAsPrimary();

      if (useRemoteConfig) {
        // Try remote config first (highest priority)
        _config = RemoteConfigService.getGoogleDriveConfig();

        if (_config != null) {
          debugPrint('Using Google Drive configuration from remote config (primary source)');
        } else {
          // Fallback to database config
          debugPrint('Remote config not available, trying database config...');
          _config = _configService.googleDriveConfig;

          if (_config != null) {
            debugPrint('Using Google Drive configuration from database (fallback)');
          }
        }
      } else {
        // Use database config first, then remote config as fallback
        _config = _configService.googleDriveConfig;

        if (_config != null) {
          debugPrint('Using Google Drive configuration from database (primary source)');
        } else {
          debugPrint('No database configuration found, trying remote config...');
          _config = RemoteConfigService.getGoogleDriveConfig();

          if (_config != null) {
            debugPrint('Using Google Drive configuration from remote config (fallback)');
          }
        }
      }

      if (_config == null) {
        throw Exception('Google Drive configuration not found. Please configure Google Drive settings in admin panel or remote config.');
      }

      // Create service account credentials from configuration
      final serviceAccount = _createServiceAccountFromConfig();

      if (serviceAccount == null) {
        throw Exception('Invalid Google Drive configuration. Please check your settings.');
      }

      final credentials = ServiceAccountCredentials.fromJson(serviceAccount);

      // Create authenticated HTTP client
      final httpClient = await clientViaServiceAccount(credentials, _scopes);

      // Initialize Drive API
      _driveApi = drive.DriveApi(httpClient);
      _isInitialized = true;

      debugPrint('Google Drive service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Google Drive service: $e');
      rethrow;
    }
  }

  // Check if service is initialized
  bool get isInitialized => _isInitialized;

  // Upload file to Google Drive
  Future<String?> uploadFile({
    required File file,
    required String fileName,
    String? folderId,
    String? mimeType,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final targetFolderId = folderId ?? _config?.parentFolderId ?? '';

      // Determine MIME type if not provided
      mimeType ??= _getMimeType(fileName);

      // Create file metadata
      final fileMetadata = drive.File()
        ..name = fileName
        ..parents = [targetFolderId];

      // Read file content
      final fileContent = await file.readAsBytes();
      final media = drive.Media(Stream.value(fileContent), fileContent.length);

      // Upload file
      final uploadedFile = await _driveApi!.files.create(
        fileMetadata,
        uploadMedia: media,
      );

      debugPrint('File uploaded successfully: ${uploadedFile.name} (ID: ${uploadedFile.id})');
      return uploadedFile.id;
    } catch (e) {
      debugPrint('Error uploading file to Google Drive: $e');
      return null;
    }
  }

  // Upload file from bytes
  Future<String?> uploadFileFromBytes({
    required Uint8List bytes,
    required String fileName,
    String? folderId,
    String? mimeType,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final targetFolderId = folderId ?? _config?.parentFolderId ?? '';

      // Determine MIME type if not provided
      mimeType ??= _getMimeType(fileName);

      // Create file metadata
      final fileMetadata = drive.File()
        ..name = fileName
        ..parents = [targetFolderId];

      // Create media from bytes
      final media = drive.Media(Stream.value(bytes), bytes.length);

      // Upload file
      final uploadedFile = await _driveApi!.files.create(
        fileMetadata,
        uploadMedia: media,
      );

      debugPrint('File uploaded successfully from bytes: ${uploadedFile.name} (ID: ${uploadedFile.id})');
      return uploadedFile.id;
    } catch (e) {
      debugPrint('Error uploading file from bytes to Google Drive: $e');
      return null;
    }
  }

  // Upload file with progress tracking
  Future<String> uploadFileWithProgress({
    required String fileName,
    required Uint8List fileData,
    required String mimeType,
    String? folderId,
    required Function(double progress) onProgress,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final targetFolderId = folderId ?? _config?.parentFolderId ?? '';

      onProgress(0.1);

      // Validate inputs
      if (fileData.isEmpty) {
        throw Exception('File data is empty');
      }

      if (fileName.isEmpty) {
        throw Exception('File name is empty');
      }

      // Create file metadata
      final fileMetadata = drive.File()
        ..name = fileName
        ..parents = [targetFolderId];

      onProgress(0.2);

      // Create media from bytes
      final media = drive.Media(Stream.value(fileData), fileData.length);

      onProgress(0.4);

      // Upload file with timeout
      final uploadedFile = await _driveApi!.files.create(
        fileMetadata,
        uploadMedia: media,
      ).timeout(
        const Duration(minutes: 5),
        onTimeout: () {
          throw Exception('Upload timeout: File upload took too long');
        },
      );

      if (uploadedFile.id == null || uploadedFile.id!.isEmpty) {
        throw Exception('Upload failed: No file ID returned');
      }

      onProgress(0.8);

      // Get public download URL
      final downloadUrl = await getPublicDownloadUrl(uploadedFile.id!);

      onProgress(1.0);

      debugPrint('File uploaded with progress: ${uploadedFile.name} (ID: ${uploadedFile.id})');

      if (downloadUrl == null || downloadUrl.isEmpty) {
        // Fallback to direct view URL
        return 'https://drive.google.com/file/d/${uploadedFile.id}/view';
      }

      return downloadUrl;

    } catch (e) {
      debugPrint('Error uploading file with progress: $e');

      // Provide more specific error messages
      if (e.toString().contains('timeout')) {
        throw Exception('Upload timeout: Please check your internet connection and try again');
      } else if (e.toString().contains('Failed to fetch')) {
        throw Exception('Network error: Please check your internet connection');
      } else if (e.toString().contains('quota')) {
        throw Exception('Storage quota exceeded: Please free up space in Google Drive');
      } else if (e.toString().contains('permission')) {
        throw Exception('Permission denied: Please check Google Drive configuration');
      }

      rethrow;
    }
  }

  // Check if service is configured
  Future<bool> isConfigured() async {
    try {
      await _configService.initialize();

      // Ensure remote config is initialized
      if (!RemoteConfigService.isInitialized) {
        await RemoteConfigService.initialize();
      }

      // Check configuration priority
      final useRemoteConfig = RemoteConfigService.useRemoteConfigAsPrimary();

      if (useRemoteConfig) {
        // Try remote config first
        _config = RemoteConfigService.getGoogleDriveConfig();

        // Fallback to database config if remote config not available
        _config ??= _configService.googleDriveConfig;
      } else {
        // Try database config first
        _config = _configService.googleDriveConfig;

        // Fallback to remote config if database config not available
        _config ??= RemoteConfigService.getGoogleDriveConfig();
      }

      if (_config == null) {
        return false;
      }

      // Check if required fields are present
      return _config!.clientEmail != null &&
             _config!.clientEmail!.isNotEmpty &&
             _config!.privateKey != null &&
             _config!.privateKey!.isNotEmpty &&
             _config!.projectId != null &&
             _config!.projectId!.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking Google Drive configuration: $e');
      return false;
    }
  }

  // Download file from Google Drive
  Future<Uint8List?> downloadFile(String fileId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('Downloading file from Google Drive (ID: $fileId)');

      final response = await _driveApi!.files.get(
        fileId,
        downloadOptions: drive.DownloadOptions.fullMedia,
      ) as drive.Media;

      final bytes = <int>[];
      await for (final chunk in response.stream) {
        bytes.addAll(chunk);
      }

      if (bytes.isEmpty) {
        debugPrint('Downloaded file is empty (ID: $fileId)');
        return null;
      }

      debugPrint('File downloaded successfully (ID: $fileId), size: ${bytes.length} bytes');
      return Uint8List.fromList(bytes);
    } catch (e) {
      debugPrint('Error downloading file from Google Drive (ID: $fileId): $e');

      // Try alternative download method for public files
      try {
        debugPrint('Trying alternative download method for file: $fileId');
        final url = 'https://drive.google.com/uc?export=download&id=$fileId';
        final response = await http.get(Uri.parse(url));

        if (response.statusCode == 200) {
          debugPrint('Alternative download successful for file: $fileId');
          return response.bodyBytes;
        }
      } catch (altError) {
        debugPrint('Alternative download also failed: $altError');
      }

      return null;
    }
  }

  // Delete file from Google Drive
  Future<bool> deleteFile(String fileId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _driveApi!.files.delete(fileId);
      debugPrint('File deleted successfully (ID: $fileId)');
      return true;
    } catch (e) {
      debugPrint('Error deleting file from Google Drive: $e');
      return false;
    }
  }

  // Get file info
  Future<drive.File?> getFileInfo(String fileId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final file = await _driveApi!.files.get(fileId) as drive.File;
      return file;
    } catch (e) {
      debugPrint('Error getting file info from Google Drive: $e');
      return null;
    }
  }

  // List files in folder
  Future<List<drive.File>> listFiles({
    String? folderId,
    String? query,
    int maxResults = 100,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final targetFolderId = folderId ?? _config?.parentFolderId ?? '';

      String searchQuery = "'$targetFolderId' in parents and trashed=false";
      if (query != null && query.isNotEmpty) {
        searchQuery += " and name contains '$query'";
      }

      final fileList = await _driveApi!.files.list(
        q: searchQuery,
        pageSize: maxResults,
        orderBy: 'createdTime desc',
      );

      return fileList.files ?? [];
    } catch (e) {
      debugPrint('Error listing files from Google Drive: $e');
      return [];
    }
  }

  // Create folder
  Future<String?> createFolder({
    required String folderName,
    String? parentFolderId,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final targetParentId = parentFolderId ?? _config?.parentFolderId ?? '';

      final folderMetadata = drive.File()
        ..name = folderName
        ..mimeType = 'application/vnd.google-apps.folder'
        ..parents = [targetParentId];

      final createdFolder = await _driveApi!.files.create(folderMetadata);

      debugPrint('Folder created successfully: $folderName (ID: ${createdFolder.id})');
      return createdFolder.id;
    } catch (e) {
      debugPrint('Error creating folder in Google Drive: $e');
      return null;
    }
  }

  // Get public download URL
  Future<String?> getPublicDownloadUrl(String fileId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Make file publicly readable
      final permission = drive.Permission()
        ..role = 'reader'
        ..type = 'anyone';

      await _driveApi!.permissions.create(permission, fileId);

      // Return direct download URL
      return 'https://drive.google.com/uc?export=download&id=$fileId';
    } catch (e) {
      debugPrint('Error getting public download URL: $e');
      return null;
    }
  }

  // Get file size
  Future<int?> getFileSize(String fileId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final file = await _driveApi!.files.get(fileId) as drive.File;
      return int.tryParse(file.size ?? '0');
    } catch (e) {
      debugPrint('Error getting file size: $e');
      return null;
    }
  }

  // Helper method to determine MIME type
  String _getMimeType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.txt':
        return 'text/plain';
      case '.json':
        return 'application/json';
      case '.zip':
        return 'application/zip';
      default:
        return 'application/octet-stream';
    }
  }

  // Get storage usage
  Future<Map<String, dynamic>?> getStorageUsage() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final about = await _driveApi!.about.get();
      final quota = about.storageQuota;

      if (quota != null) {
        return {
          'limit': int.tryParse(quota.limit ?? '0') ?? 0,
          'usage': int.tryParse(quota.usage ?? '0') ?? 0,
          'usageInDrive': int.tryParse(quota.usageInDrive ?? '0') ?? 0,
        };
      }
      return null;
    } catch (e) {
      debugPrint('Error getting storage usage: $e');
      return null;
    }
  }

  // Create service account from config (using database configuration)
  Map<String, dynamic>? _createServiceAccountFromConfig() {
    if (_config == null) return null;

    try {
      // Validate required fields
      if (_config!.clientEmail == null || _config!.clientEmail!.isEmpty ||
          _config!.privateKey == null || _config!.privateKey!.isEmpty ||
          _config!.projectId == null || _config!.projectId!.isEmpty) {
        debugPrint('Missing required Google Drive configuration fields');
        return null;
      }

      // Validate and fix private key format
      String privateKey = _config!.privateKey!.trim();

      // Check if private key has correct PEM format
      if (!privateKey.startsWith('-----BEGIN PRIVATE KEY-----') &&
          !privateKey.startsWith('-----BEGIN RSA PRIVATE KEY-----')) {
        debugPrint('Private key does not have correct PEM format');

        // Try to fix the format if it's just the key content without headers
        if (privateKey.length > 100 && !privateKey.contains('-----')) {
          privateKey = '-----BEGIN PRIVATE KEY-----\n$privateKey\n-----END PRIVATE KEY-----';
          debugPrint('Fixed private key format by adding PEM headers');
        } else {
          throw Exception('Invalid private key format. Please ensure it includes PEM headers (-----BEGIN PRIVATE KEY----- and -----END PRIVATE KEY-----)');
        }
      }

      // Ensure proper line breaks in private key
      privateKey = privateKey.replaceAll('\\n', '\n');

      // Create service account structure from database config
      return {
        "type": "service_account",
        "project_id": _config!.projectId!,
        "private_key_id": _config!.privateKeyId ?? "",
        "private_key": privateKey,
        "client_email": _config!.clientEmail!,
        "client_id": _config!.clientId,
        "auth_uri": _config!.authUri ?? "https://accounts.google.com/o/oauth2/auth",
        "token_uri": _config!.tokenUri ?? "https://oauth2.googleapis.com/token",
        "auth_provider_x509_cert_url": _config!.authProviderCertUrl ?? "https://www.googleapis.com/oauth2/v1/certs",
        "client_x509_cert_url": _config!.clientCertUrl ?? "",
        "universe_domain": _config!.universeDomain ?? "googleapis.com"
      };
    } catch (e) {
      debugPrint('Error creating service account from config: $e');
      return null;
    }
  }

  // Dispose resources
  void dispose() {
    _driveApi = null;
    _isInitialized = false;
  }
}

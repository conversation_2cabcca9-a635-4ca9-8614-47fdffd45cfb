import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// فئة تحسين الخطوط لتقليل حجم التطبيق وتحسين الأداء
class FontOptimization {
  static const String _defaultFontFamily = 'Roboto';
  static const String _arabicFontFamily = 'Cairo';
  
  // خطوط محسنة للأداء
  static const Map<String, FontWeight> _optimizedFontWeights = {
    'light': FontWeight.w300,
    'regular': FontWeight.w400,
    'medium': FontWeight.w500,
    'semibold': FontWeight.w600,
    'bold': FontWeight.w700,
  };

  /// الحصول على نمط نص محسن
  static TextStyle getOptimizedTextStyle({
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
    bool isArabic = false,
    double? letterSpacing,
    double? height,
  }) {
    return TextStyle(
      fontFamily: isArabic ? _arabicFontFamily : _defaultFontFamily,
      fontSize: fontSize,
      fontWeight: _getOptimizedFontWeight(fontWeight),
      color: color,
      letterSpacing: letterSpacing,
      height: height,
      // تحسين الرسم
      textBaseline: TextBaseline.alphabetic,
    );
  }

  /// الحصول على وزن خط محسن
  static FontWeight _getOptimizedFontWeight(FontWeight weight) {
    // تقليل عدد أوزان الخطوط المستخدمة لتوفير المساحة
    if (weight.index <= FontWeight.w300.index) {
      return FontWeight.w300;
    } else if (weight.index <= FontWeight.w400.index) {
      return FontWeight.w400;
    } else if (weight.index <= FontWeight.w500.index) {
      return FontWeight.w500;
    } else if (weight.index <= FontWeight.w600.index) {
      return FontWeight.w600;
    } else {
      return FontWeight.w700;
    }
  }

  /// أنماط نصوص محسنة للعناوين
  static TextStyle getHeadingStyle({
    required int level, // 1-6
    Color? color,
    bool isArabic = false,
  }) {
    final Map<int, double> headingSizes = {
      1: 32,
      2: 28,
      3: 24,
      4: 20,
      5: 18,
      6: 16,
    };

    final Map<int, FontWeight> headingWeights = {
      1: FontWeight.w700,
      2: FontWeight.w700,
      3: FontWeight.w600,
      4: FontWeight.w600,
      5: FontWeight.w500,
      6: FontWeight.w500,
    };

    return getOptimizedTextStyle(
      fontSize: headingSizes[level] ?? 16,
      fontWeight: headingWeights[level] ?? FontWeight.normal,
      color: color,
      isArabic: isArabic,
      height: 1.2,
    );
  }

  /// نمط نص للجسم الرئيسي
  static TextStyle getBodyStyle({
    Color? color,
    bool isArabic = false,
    bool isLarge = false,
  }) {
    return getOptimizedTextStyle(
      fontSize: isLarge ? 16 : 14,
      fontWeight: FontWeight.w400,
      color: color,
      isArabic: isArabic,
      height: 1.5,
    );
  }

  /// نمط نص للتسميات
  static TextStyle getLabelStyle({
    Color? color,
    bool isArabic = false,
    bool isSmall = false,
  }) {
    return getOptimizedTextStyle(
      fontSize: isSmall ? 10 : 12,
      fontWeight: FontWeight.w500,
      color: color,
      isArabic: isArabic,
      letterSpacing: 0.5,
    );
  }

  /// نمط نص للأزرار
  static TextStyle getButtonStyle({
    Color? color,
    bool isArabic = false,
    bool isLarge = false,
  }) {
    return getOptimizedTextStyle(
      fontSize: isLarge ? 16 : 14,
      fontWeight: FontWeight.w600,
      color: color,
      isArabic: isArabic,
      letterSpacing: 0.8,
    );
  }

  /// نمط نص للتسميات التوضيحية
  static TextStyle getCaptionStyle({
    Color? color,
    bool isArabic = false,
  }) {
    return getOptimizedTextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: color,
      isArabic: isArabic,
      height: 1.3,
    );
  }

  /// نمط نص للعناوين الفرعية
  static TextStyle getSubtitleStyle({
    Color? color,
    bool isArabic = false,
    int level = 1, // 1 أو 2
  }) {
    return getOptimizedTextStyle(
      fontSize: level == 1 ? 16 : 14,
      fontWeight: level == 1 ? FontWeight.w500 : FontWeight.w400,
      color: color,
      isArabic: isArabic,
      height: 1.4,
    );
  }

  /// تحميل الخطوط المطلوبة فقط
  static Future<void> preloadEssentialFonts() async {
    try {
      // تحميل الخطوط الأساسية فقط
      await Future.wait([
        _loadFontVariant(_defaultFontFamily, FontWeight.w400),
        _loadFontVariant(_defaultFontFamily, FontWeight.w500),
        _loadFontVariant(_defaultFontFamily, FontWeight.w600),
        _loadFontVariant(_defaultFontFamily, FontWeight.w700),
        _loadFontVariant(_arabicFontFamily, FontWeight.w400),
        _loadFontVariant(_arabicFontFamily, FontWeight.w500),
        _loadFontVariant(_arabicFontFamily, FontWeight.w600),
        _loadFontVariant(_arabicFontFamily, FontWeight.w700),
      ]);
      debugPrint('✅ Essential fonts preloaded successfully');
    } catch (e) {
      debugPrint('❌ Error preloading fonts: $e');
    }
  }

  /// تحميل متغير خط معين
  static Future<void> _loadFontVariant(String fontFamily, FontWeight weight) async {
    try {
      final TextStyle style = TextStyle(
        fontFamily: fontFamily,
        fontWeight: weight,
      );
      
      // إنشاء نص وهمي لتحميل الخط
      final TextPainter painter = TextPainter(
        text: TextSpan(text: 'Loading...', style: style),
        textDirection: TextDirection.ltr,
      );
      
      painter.layout();
      painter.dispose();
    } catch (e) {
      debugPrint('Error loading font variant $fontFamily $weight: $e');
    }
  }

  /// تنظيف كاش الخطوط
  static void clearFontCache() {
    // Flutter يدير كاش الخطوط تلقائياً
    // هذه الدالة للاستخدام المستقبلي إذا احتجنا تنظيف يدوي
    debugPrint('Font cache cleared');
  }

  /// الحصول على حجم خط متجاوب
  static double getResponsiveFontSize(
    BuildContext context,
    double baseFontSize, {
    double mobileScale = 1.0,
    double tabletScale = 1.1,
    double desktopScale = 1.2,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return baseFontSize * mobileScale;
    } else if (screenWidth < 1200) {
      return baseFontSize * tabletScale;
    } else {
      return baseFontSize * desktopScale;
    }
  }

  /// تحسين نص للقراءة
  static TextStyle getReadableTextStyle(
    BuildContext context, {
    Color? color,
    bool isArabic = false,
  }) {
    final brightness = Theme.of(context).brightness;
    final defaultColor = brightness == Brightness.dark 
        ? Colors.white.withOpacity(0.87)
        : Colors.black.withOpacity(0.87);

    return getOptimizedTextStyle(
      fontSize: getResponsiveFontSize(context, 16),
      fontWeight: FontWeight.w400,
      color: color ?? defaultColor,
      isArabic: isArabic,
      height: 1.6, // تحسين المسافة بين الأسطر للقراءة
      letterSpacing: isArabic ? 0.0 : 0.2,
    );
  }

  /// نمط نص للأخطاء
  static TextStyle getErrorTextStyle({
    bool isArabic = false,
    double fontSize = 14,
  }) {
    return getOptimizedTextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w500,
      color: Colors.red.shade700,
      isArabic: isArabic,
    );
  }

  /// نمط نص للنجاح
  static TextStyle getSuccessTextStyle({
    bool isArabic = false,
    double fontSize = 14,
  }) {
    return getOptimizedTextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w500,
      color: Colors.green.shade700,
      isArabic: isArabic,
    );
  }

  /// نمط نص للتحذيرات
  static TextStyle getWarningTextStyle({
    bool isArabic = false,
    double fontSize = 14,
  }) {
    return getOptimizedTextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w500,
      color: Colors.orange.shade700,
      isArabic: isArabic,
    );
  }

  /// نمط نص للمعلومات
  static TextStyle getInfoTextStyle({
    bool isArabic = false,
    double fontSize = 14,
  }) {
    return getOptimizedTextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w500,
      color: Colors.blue.shade700,
      isArabic: isArabic,
    );
  }

  /// تحسين نص للعرض على الخلفيات الملونة
  static TextStyle getContrastTextStyle(
    Color backgroundColor, {
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.normal,
    bool isArabic = false,
  }) {
    // حساب التباين وتحديد لون النص المناسب
    final luminance = backgroundColor.computeLuminance();
    final textColor = luminance > 0.5 ? Colors.black87 : Colors.white;

    return getOptimizedTextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: textColor,
      isArabic: isArabic,
    );
  }

  /// الحصول على قائمة الخطوط المحسنة
  static List<String> getOptimizedFontList() {
    return [
      _defaultFontFamily,
      _arabicFontFamily,
    ];
  }

  /// تحديد ما إذا كان النص يحتوي على أحرف عربية
  static bool containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  /// الحصول على نمط نص تلقائي حسب المحتوى
  static TextStyle getAutoTextStyle(
    String text, {
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
  }) {
    final isArabic = containsArabic(text);
    return getOptimizedTextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      isArabic: isArabic,
    );
  }
}

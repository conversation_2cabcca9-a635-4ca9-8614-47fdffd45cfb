import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;

/// فئة تحسين الصور لتقليل حجم التطبيق وتحسين الأداء
class ImageOptimization {
  static const int _defaultQuality = 85;
  static const int _maxWidth = 1024;
  static const int _maxHeight = 1024;
  static const int _thumbnailSize = 256;

  /// ضغط صورة من البايتات
  static Future<Uint8List> compressImageBytes(
    Uint8List imageBytes, {
    int quality = _defaultQuality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) return imageBytes;

      // تحديد الأبعاد الجديدة
      final targetWidth = maxWidth ?? _maxWidth;
      final targetHeight = maxHeight ?? _maxHeight;

      // تغيير حجم الصورة إذا كانت أكبر من الحد المسموح
      img.Image resizedImage = image;
      if (image.width > targetWidth || image.height > targetHeight) {
        resizedImage = img.copyResize(
          image,
          width: image.width > targetWidth ? targetWidth : null,
          height: image.height > targetHeight ? targetHeight : null,
          interpolation: img.Interpolation.linear,
        );
      }

      // ضغط الصورة
      final compressedBytes = img.encodeJpg(resizedImage, quality: quality);
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return imageBytes;
    }
  }

  /// إنشاء صورة مصغرة
  static Future<Uint8List> createThumbnail(
    Uint8List imageBytes, {
    int size = _thumbnailSize,
    int quality = _defaultQuality,
  }) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) return imageBytes;

      // إنشاء صورة مربعة مصغرة
      final thumbnail = img.copyResizeCropSquare(image, size: size);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: quality);
      return Uint8List.fromList(thumbnailBytes);
    } catch (e) {
      debugPrint('Error creating thumbnail: $e');
      return imageBytes;
    }
  }

  /// تحسين صورة من Asset
  static Future<Uint8List?> optimizeAssetImage(
    String assetPath, {
    int quality = _defaultQuality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final ByteData data = await rootBundle.load(assetPath);
      final Uint8List bytes = data.buffer.asUint8List();
      return await compressImageBytes(
        bytes,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );
    } catch (e) {
      debugPrint('Error optimizing asset image: $e');
      return null;
    }
  }

  /// تحويل Widget إلى صورة محسنة
  static Future<Uint8List?> widgetToOptimizedImage(
    Widget widget, {
    Size size = const Size(200, 200),
    int quality = _defaultQuality,
  }) async {
    try {
      final RenderRepaintBoundary repaintBoundary = RenderRepaintBoundary();
      final RenderView renderView = RenderView(
        child: RenderPositionedBox(
          alignment: Alignment.center,
          child: repaintBoundary,
        ),
        configuration: ViewConfiguration(
          size: size,
          devicePixelRatio: 1.0,
        ),
        view: WidgetsBinding.instance.platformDispatcher.views.first,
      );

      final PipelineOwner pipelineOwner = PipelineOwner();
      final BuildOwner buildOwner = BuildOwner(focusManager: FocusManager());

      pipelineOwner.rootNode = renderView;
      renderView.prepareInitialFrame();

      final RenderObjectToWidgetElement<RenderBox> rootElement =
          RenderObjectToWidgetAdapter<RenderBox>(
        container: repaintBoundary,
        child: widget,
      ).attachToRenderTree(buildOwner);

      buildOwner.buildScope(rootElement);
      buildOwner.finalizeTree();
      pipelineOwner.flushLayout();
      pipelineOwner.flushCompositingBits();
      pipelineOwner.flushPaint();

      final ui.Image image = await repaintBoundary.toImage(pixelRatio: 1.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) return null;

      final Uint8List pngBytes = byteData.buffer.asUint8List();
      return await compressImageBytes(pngBytes, quality: quality);
    } catch (e) {
      debugPrint('Error converting widget to image: $e');
      return null;
    }
  }

  /// تحسين مجموعة من الصور
  static Future<List<Uint8List>> optimizeImageBatch(
    List<Uint8List> imageList, {
    int quality = _defaultQuality,
    int? maxWidth,
    int? maxHeight,
  }) async {
    final List<Uint8List> optimizedImages = [];
    
    for (final imageBytes in imageList) {
      try {
        final optimized = await compressImageBytes(
          imageBytes,
          quality: quality,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
        );
        optimizedImages.add(optimized);
      } catch (e) {
        debugPrint('Error optimizing image in batch: $e');
        optimizedImages.add(imageBytes); // إضافة الصورة الأصلية في حالة الخطأ
      }
    }
    
    return optimizedImages;
  }

  /// حساب حجم الصورة بالبايت
  static int getImageSize(Uint8List imageBytes) {
    return imageBytes.length;
  }

  /// حساب نسبة الضغط
  static double getCompressionRatio(Uint8List original, Uint8List compressed) {
    if (original.isEmpty) return 0.0;
    return (1.0 - (compressed.length / original.length)) * 100;
  }

  /// تحسين صورة للعرض في الشبكة
  static Future<Uint8List> optimizeForGrid(
    Uint8List imageBytes, {
    int gridSize = 200,
    int quality = 80,
  }) async {
    return await compressImageBytes(
      imageBytes,
      quality: quality,
      maxWidth: gridSize,
      maxHeight: gridSize,
    );
  }

  /// تحسين صورة للعرض في القائمة
  static Future<Uint8List> optimizeForList(
    Uint8List imageBytes, {
    int listHeight = 100,
    int quality = 75,
  }) async {
    return await compressImageBytes(
      imageBytes,
      quality: quality,
      maxHeight: listHeight,
    );
  }

  /// تحسين صورة للعرض الكامل
  static Future<Uint8List> optimizeForFullView(
    Uint8List imageBytes, {
    int quality = 90,
  }) async {
    return await compressImageBytes(
      imageBytes,
      quality: quality,
      maxWidth: 1920,
      maxHeight: 1080,
    );
  }

  /// تحديد ما إذا كانت الصورة تحتاج إلى تحسين
  static bool needsOptimization(
    Uint8List imageBytes, {
    int maxSizeKB = 500,
  }) {
    final sizeKB = imageBytes.length / 1024;
    return sizeKB > maxSizeKB;
  }

  /// تحسين تلقائي للصورة حسب الاستخدام
  static Future<Uint8List> autoOptimize(
    Uint8List imageBytes, {
    ImageUsageType usage = ImageUsageType.general,
  }) async {
    switch (usage) {
      case ImageUsageType.thumbnail:
        return await createThumbnail(imageBytes);
      case ImageUsageType.grid:
        return await optimizeForGrid(imageBytes);
      case ImageUsageType.list:
        return await optimizeForList(imageBytes);
      case ImageUsageType.fullView:
        return await optimizeForFullView(imageBytes);
      case ImageUsageType.general:
      default:
        return await compressImageBytes(imageBytes);
    }
  }

  /// تنظيف ذاكرة الصور
  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// تحسين إعدادات كاش الصور
  static void optimizeImageCacheSettings({
    int maxCacheSize = 100,
    int maxCacheSizeBytes = 50 * 1024 * 1024, // 50 MB
  }) {
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.maximumSize = maxCacheSize;
    imageCache.maximumSizeBytes = maxCacheSizeBytes;
  }
}

/// أنواع استخدام الصور
enum ImageUsageType {
  thumbnail,
  grid,
  list,
  fullView,
  general,
}

/// معلومات تحسين الصورة
class ImageOptimizationInfo {
  final int originalSize;
  final int optimizedSize;
  final double compressionRatio;
  final Duration processingTime;

  ImageOptimizationInfo({
    required this.originalSize,
    required this.optimizedSize,
    required this.compressionRatio,
    required this.processingTime,
  });

  @override
  String toString() {
    return 'ImageOptimizationInfo('
        'originalSize: ${(originalSize / 1024).toStringAsFixed(2)} KB, '
        'optimizedSize: ${(optimizedSize / 1024).toStringAsFixed(2)} KB, '
        'compressionRatio: ${compressionRatio.toStringAsFixed(2)}%, '
        'processingTime: ${processingTime.inMilliseconds} ms'
        ')';
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A reusable widget for displaying the app logo with SVG support and enhanced circular design.
class AppLogo extends StatelessWidget {
  const AppLogo({
    super.key,
    this.size = 50.0,
    this.padding = const EdgeInsets.all(8.0),
    this.backgroundColor = Colors.transparent,
    this.borderRadius = 8, // Default rounded corners instead of circular
    this.showShadow = true,
    this.onTap,
    this.heroTag,
  });

  /// The size of the logo.
  final double size;

  /// Padding around the logo.
  final EdgeInsetsGeometry padding;

  /// Background color of the logo container.
  final Color backgroundColor;

  /// Border radius of the logo container. Use -1 for fully circular, or any positive value for rounded corners.
  final double borderRadius;

  /// Whether to show shadow effect.
  final bool showShadow;

  /// Callback when logo is tapped.
  final VoidCallback? onTap;

  /// Hero tag for animations.
  final String? heroTag;

  @override
  Widget build(BuildContext context) {
    final isCircular = borderRadius == -1;
    final actualRadius = isCircular ? size / 2 : borderRadius;

    Widget logoWidget = Container(
      width: size,
      height: size,
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(actualRadius),
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(actualRadius),
        child: _buildLogoContent(),
      ),
    );

    // Add hero animation if heroTag is provided
    if (heroTag != null) {
      logoWidget = Hero(
        tag: heroTag!,
        child: logoWidget,
      );
    }

    // Add tap functionality if onTap is provided
    if (onTap != null) {
      logoWidget = GestureDetector(
        onTap: onTap,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(actualRadius),
            onTap: onTap,
            child: logoWidget,
          ),
        ),
      );
    }

    return logoWidget;
  }

  Widget _buildLogoContent() {
    return SvgPicture.asset(
      'assets/images/logo.svg',
      width: size,
      height: size,
      fit: BoxFit.contain,
      placeholderBuilder: (context) => _buildFallbackLogo(),
    );
  }

  Widget _buildFallbackLogo() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(borderRadius == -1 ? size / 2 : borderRadius),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(
            Icons.settings,
            size: size * 0.4,
            color: Colors.white,
          ),
          Positioned(
            top: size * 0.15,
            right: size * 0.15,
            child: Container(
              width: size * 0.2,
              height: size * 0.2,
              decoration: const BoxDecoration(
                color: Color(0xFFFF5722),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error,
                size: size * 0.12,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying logo in a dialog when tapped
class LogoDisplayDialog extends StatelessWidget {
  const LogoDisplayDialog({super.key});

  static void show(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const LogoDisplayDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppLogo(
              size: 120,
              heroTag: 'app_logo',
              showShadow: false,
            ),
            const SizedBox(height: 16),
            Text(
              'HM Device Errors',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'نظام إدارة أعطال الأجهزة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      ),
    );
  }
}

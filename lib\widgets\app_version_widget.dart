import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppVersionWidget extends StatefulWidget {
  final TextStyle? style;
  final bool showBuildNumber;
  final String prefix;
  final bool isRTL;

  const AppVersionWidget({
    super.key,
    this.style,
    this.showBuildNumber = false,
    this.prefix = '',
    this.isRTL = false,
  });

  @override
  State<AppVersionWidget> createState() => _AppVersionWidgetState();
}

class _AppVersionWidgetState extends State<AppVersionWidget> {
  String _version = '';
  String _buildNumber = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadVersion();
  }

  Future<void> _loadVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = packageInfo.version;
        _buildNumber = packageInfo.buildNumber;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _version = '1.0.4'; // Fallback version
        _buildNumber = '5';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.style?.color ?? Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey,
          ),
        ),
      );
    }

    String displayText = widget.prefix;
    if (widget.isRTL) {
      displayText += widget.showBuildNumber ? '$_version+$_buildNumber' : _version;
    } else {
      displayText += widget.showBuildNumber ? '$_version+$_buildNumber' : _version;
    }

    return Text(
      displayText,
      style: widget.style,
    );
  }
}

class AppVersionInfo extends StatelessWidget {
  final bool isRTL;
  final bool showDetails;

  const AppVersionInfo({
    super.key,
    this.isRTL = false,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return Text(
            isRTL ? 'الإصدار: 1.0.4' : 'Version: 1.0.4',
            style: TextStyle(color: Colors.grey[600]),
          );
        }

        final packageInfo = snapshot.data!;
        
        if (!showDetails) {
          return Text(
            '${isRTL ? 'الإصدار: ' : 'Version: '}${packageInfo.version}',
            style: TextStyle(color: Colors.grey[600]),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${isRTL ? 'الإصدار: ' : 'Version: '}${packageInfo.version}',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 4),
            Text(
              '${isRTL ? 'رقم البناء: ' : 'Build: '}${packageInfo.buildNumber}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            const SizedBox(height: 4),
            Text(
              '${isRTL ? 'اسم الحزمة: ' : 'Package: '}${packageInfo.packageName}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        );
      },
    );
  }
}

class AppVersionCard extends StatelessWidget {
  final bool isRTL;

  const AppVersionCard({
    super.key,
    this.isRTL = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                Text(
                  isRTL ? 'معلومات التطبيق' : 'App Information',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            AppVersionInfo(
              isRTL: isRTL,
              showDetails: true,
            ),
          ],
        ),
      ),
    );
  }
}

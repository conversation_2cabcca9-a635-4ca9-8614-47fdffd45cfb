import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/attachment_model.dart';
import '../providers/locale_provider.dart';

class AttachmentViewerDialog extends StatefulWidget {
  final List<AttachmentModel> attachments;
  final int initialIndex;
  final Function(AttachmentModel)? onDelete;
  final Function(AttachmentModel)? onDownload;
  final Function(AttachmentModel)? onShare;
  final bool showActions;

  const AttachmentViewerDialog({
    super.key,
    required this.attachments,
    required this.initialIndex,
    this.onDelete,
    this.onDownload,
    this.onShare,
    this.showActions = true,
  });

  @override
  State<AttachmentViewerDialog> createState() => _AttachmentViewerDialogState();
}

class _AttachmentViewerDialogState extends State<AttachmentViewerDialog> {
  late PageController _pageController;
  late int _currentIndex;
  bool _showUI = true;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final currentAttachment = widget.attachments[_currentIndex];

    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () {
          setState(() {
            _showUI = !_showUI;
          });
        },
        child: Stack(
          children: [
            // Main content viewer
            PageView.builder(
              controller: _pageController,
              itemCount: widget.attachments.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                final attachment = widget.attachments[index];
                return _buildAttachmentViewer(attachment);
              },
            ),

            // Top bar with info and close button
            if (_showUI)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withAlpha(180),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // Close button
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(Icons.close, color: Colors.white, size: 28),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.black.withAlpha(100),
                              shape: const CircleBorder(),
                            ),
                          ),
                          const SizedBox(width: 12),

                          // File info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  currentAttachment.originalFileName,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: _getTypeColor(currentAttachment.type),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        _getTypeLabel(currentAttachment.type),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      _formatFileSize(currentAttachment.fileSize),
                                      style: TextStyle(
                                        color: Colors.white.withAlpha(200),
                                        fontSize: 12,
                                      ),
                                    ),
                                    if (widget.attachments.length > 1) ...[
                                      const SizedBox(width: 8),
                                      Text(
                                        '${_currentIndex + 1} / ${widget.attachments.length}',
                                        style: TextStyle(
                                          color: Colors.white.withAlpha(200),
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // Bottom action bar
            if (_showUI && widget.showActions)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withAlpha(180),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Download button
                          if (widget.onDownload != null)
                            _buildActionButton(
                              icon: Icons.download,
                              label: isRTL ? 'تحميل' : 'Download',
                              onPressed: () => widget.onDownload!(currentAttachment),
                            ),

                          // Share button
                          if (widget.onShare != null)
                            _buildActionButton(
                              icon: Icons.share,
                              label: isRTL ? 'مشاركة' : 'Share',
                              onPressed: () => widget.onShare!(currentAttachment),
                            ),

                          // Delete button
                          if (widget.onDelete != null)
                            _buildActionButton(
                              icon: Icons.delete,
                              label: isRTL ? 'حذف' : 'Delete',
                              color: Colors.red,
                              onPressed: () => _showDeleteConfirmation(currentAttachment),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // Page indicator for multiple attachments
            if (_showUI && widget.attachments.length > 1)
              Positioned(
                bottom: widget.showActions ? 120 : 40,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(150),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(
                        widget.attachments.length,
                        (index) => Container(
                          margin: const EdgeInsets.symmetric(horizontal: 2),
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: index == _currentIndex
                                ? Colors.white
                                : Colors.white.withAlpha(100),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentViewer(AttachmentModel attachment) {
    if (attachment.isImage) {
      return _buildImageViewer(attachment);
    } else if (attachment.isVideo) {
      return _buildVideoViewer(attachment);
    } else {
      return _buildDocumentViewer(attachment);
    }
  }

  Widget _buildImageViewer(AttachmentModel attachment) {
    return Center(
      child: InteractiveViewer(
        minScale: 0.5,
        maxScale: 4.0,
        child: CachedNetworkImage(
          imageUrl: attachment.url,
          fit: BoxFit.contain,
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
          errorWidget: (context, url, error) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.white, size: 48),
                const SizedBox(height: 16),
                Text(
                  'فشل في تحميل الصورة',
                  style: TextStyle(color: Colors.white.withAlpha(200)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoViewer(AttachmentModel attachment) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(50),
              borderRadius: BorderRadius.circular(60),
              border: Border.all(color: Colors.blue, width: 2),
            ),
            child: const Icon(
              Icons.play_circle_outline,
              color: Colors.blue,
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            attachment.originalFileName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط للتحميل والتشغيل',
            style: TextStyle(
              color: Colors.white.withAlpha(200),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentViewer(AttachmentModel attachment) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: _getTypeColor(attachment.type).withAlpha(50),
              borderRadius: BorderRadius.circular(60),
              border: Border.all(color: _getTypeColor(attachment.type), width: 2),
            ),
            child: Icon(
              _getTypeIcon(attachment.type),
              color: _getTypeColor(attachment.type),
              size: 60,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            attachment.originalFileName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _formatFileSize(attachment.fileSize),
            style: TextStyle(
              color: Colors.white.withAlpha(200),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: (color ?? Colors.white).withAlpha(30),
            borderRadius: BorderRadius.circular(28),
            border: Border.all(
              color: color ?? Colors.white,
              width: 1.5,
            ),
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(
              icon,
              color: color ?? Colors.white,
              size: 24,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            color: color ?? Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(AttachmentModel attachment) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isRTL = localeProvider.isRTL;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'تأكيد الحذف' : 'Confirm Delete'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من حذف هذا المرفق؟\n${attachment.originalFileName}'
              : 'Are you sure you want to delete this attachment?\n${attachment.originalFileName}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Close viewer
              widget.onDelete!(attachment);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return Colors.green;
      case AttachmentType.video:
        return Colors.blue;
      case AttachmentType.document:
        return Colors.orange;
    }
  }

  String _getTypeLabel(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return 'صورة';
      case AttachmentType.video:
        return 'فيديو';
      case AttachmentType.document:
        return 'مستند';
    }
  }

  IconData _getTypeIcon(AttachmentType type) {
    switch (type) {
      case AttachmentType.image:
        return Icons.image;
      case AttachmentType.video:
        return Icons.videocam;
      case AttachmentType.document:
        return Icons.description;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

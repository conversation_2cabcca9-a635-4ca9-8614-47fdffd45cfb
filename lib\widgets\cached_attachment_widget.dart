import 'dart:io';
import 'package:flutter/material.dart';
import '../models/attachment_model.dart';
import '../services/attachment_cache_service.dart';

/// Widget محسن لعرض المرفقات مع دعم الـ cache المتقدم
class CachedAttachmentWidget extends StatefulWidget {
  final AttachmentModel attachment;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool showLoadingProgress;
  final bool enableThumbnail;
  final VoidCallback? onTap;
  final Widget? errorWidget;
  final Widget? placeholder;

  const CachedAttachmentWidget({
    super.key,
    required this.attachment,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.showLoadingProgress = true,
    this.enableThumbnail = true,
    this.onTap,
    this.errorWidget,
    this.placeholder,
  });

  @override
  State<CachedAttachmentWidget> createState() => _CachedAttachmentWidgetState();
}

class _CachedAttachmentWidgetState extends State<CachedAttachmentWidget> {
  File? _cachedFile;
  File? _cachedThumbnail;
  bool _isLoading = false;
  bool _hasError = false;
  double _downloadProgress = 0.0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadCachedFile();
  }

  @override
  void didUpdateWidget(CachedAttachmentWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.attachment.url != widget.attachment.url) {
      _loadCachedFile();
    }
  }

  Future<void> _loadCachedFile() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    try {
      final cacheService = AttachmentCacheService.instance;

      // البحث عن الملف في الـ cache أولاً
      File? cachedFile = await cacheService.getCachedFile(widget.attachment);
      File? cachedThumbnail;

      if (widget.enableThumbnail && widget.attachment.isImage) {
        cachedThumbnail = await cacheService.getCachedThumbnail(widget.attachment);
      }

      if (cachedFile != null) {
        // الملف موجود في الـ cache
        setState(() {
          _cachedFile = cachedFile;
          _cachedThumbnail = cachedThumbnail;
          _isLoading = false;
        });
      } else {
        // تحميل الملف وحفظه في الـ cache
        await _downloadAndCacheFile();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _downloadAndCacheFile() async {
    try {
      final cacheService = AttachmentCacheService.instance;

      // محاكاة progress للتحميل
      _updateProgress(0.1);

      final cachedFile = await cacheService.cacheFile(
        widget.attachment,
        generateThumbnail: widget.enableThumbnail,
      );

      _updateProgress(0.8);

      File? cachedThumbnail;
      if (widget.enableThumbnail && widget.attachment.isImage) {
        cachedThumbnail = await cacheService.getCachedThumbnail(widget.attachment);
      }

      _updateProgress(1.0);

      if (cachedFile != null) {
        setState(() {
          _cachedFile = cachedFile;
          _cachedThumbnail = cachedThumbnail;
          _isLoading = false;
        });
      } else {
        throw Exception('فشل في تحميل الملف');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  void _updateProgress(double progress) {
    if (mounted) {
      setState(() {
        _downloadProgress = progress;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildContent(),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_cachedFile != null) {
      return _buildCachedFileWidget();
    }

    return _buildPlaceholderWidget();
  }

  Widget _buildCachedFileWidget() {
    switch (widget.attachment.type) {
      case AttachmentType.image:
        // استخدام thumbnail إذا كان متاحاً ومطلوباً
        final fileToShow = (widget.enableThumbnail && _cachedThumbnail != null)
            ? _cachedThumbnail!
            : _cachedFile!;

        return Stack(
          fit: StackFit.expand,
          children: [
            Image.file(
              fileToShow,
              fit: widget.fit,
              errorBuilder: (context, error, stackTrace) {
                return _buildErrorWidget();
              },
            ),
            // مؤشر الـ cache
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.offline_pin,
                  size: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );

      case AttachmentType.video:
        return Stack(
          fit: StackFit.expand,
          children: [
            Container(
              color: Colors.black12,
              child: const Icon(
                Icons.videocam,
                size: 48,
                color: Colors.blue,
              ),
            ),
            const Center(
              child: Icon(
                Icons.play_circle_outline,
                size: 32,
                color: Colors.white,
              ),
            ),
            // مؤشر الـ cache
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.offline_pin,
                  size: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );

      case AttachmentType.document:
        return Stack(
          fit: StackFit.expand,
          children: [
            Container(
              color: Colors.orange.withValues(alpha: 0.1),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.description,
                    size: 48,
                    color: Colors.orange,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.attachment.fileExtension.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
            // مؤشر الـ cache
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.offline_pin,
                  size: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
    }
  }

  Widget _buildLoadingWidget() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      color: Colors.grey.shade100,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.showLoadingProgress) ...[
            CircularProgressIndicator(
              value: _downloadProgress,
              strokeWidth: 3,
            ),
            const SizedBox(height: 8),
            Text(
              '${(_downloadProgress * 100).toInt()}%',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'جاري التحميل...',
              style: TextStyle(fontSize: 10),
            ),
          ] else ...[
            const CircularProgressIndicator(strokeWidth: 3),
            const SizedBox(height: 8),
            const Text(
              'جاري التحميل...',
              style: TextStyle(fontSize: 10),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      color: Colors.red.shade50,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 32,
            color: Colors.red.shade600,
          ),
          const SizedBox(height: 4),
          Text(
            'فشل التحميل',
            style: TextStyle(
              fontSize: 10,
              color: Colors.red.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_errorMessage != null) ...[
            const SizedBox(height: 2),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 8,
                color: Colors.red.shade400,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const SizedBox(height: 4),
          GestureDetector(
            onTap: _loadCachedFile,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.shade600,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'إعادة المحاولة',
                style: TextStyle(
                  fontSize: 8,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderWidget() {
    return Container(
      color: Colors.grey.shade200,
      child: const Center(
        child: Icon(
          Icons.attach_file,
          size: 32,
          color: Colors.grey,
        ),
      ),
    );
  }
}

/// Widget مبسط لعرض معاينة المرفق
class AttachmentPreviewWidget extends StatelessWidget {
  final AttachmentModel attachment;
  final VoidCallback? onTap;

  const AttachmentPreviewWidget({
    super.key,
    required this.attachment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CachedAttachmentWidget(
      attachment: attachment,
      width: 80,
      height: 80,
      enableThumbnail: true,
      showLoadingProgress: false,
      onTap: onTap,
    );
  }
}

/// Widget لعرض المرفق بالحجم الكامل
class FullSizeAttachmentWidget extends StatelessWidget {
  final AttachmentModel attachment;

  const FullSizeAttachmentWidget({
    super.key,
    required this.attachment,
  });

  @override
  Widget build(BuildContext context) {
    return CachedAttachmentWidget(
      attachment: attachment,
      fit: BoxFit.contain,
      enableThumbnail: false,
      showLoadingProgress: true,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/device_error_model.dart';
import '../providers/category_provider.dart';
import '../providers/locale_provider.dart';
import '../screens/errors/error_details_screen.dart';

class EnhancedErrorCard extends StatelessWidget {
  final DeviceError error;
  final bool isCompact;
  final VoidCallback? onFavoritePressed;
  final bool showFavoriteButton;

  const EnhancedErrorCard({
    super.key,
    required this.error,
    this.isCompact = false,
    this.onFavoritePressed,
    this.showFavoriteButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isCompact ? 6 : 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDark
              ? Theme.of(context).colorScheme.outline.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(100), // حدود زرقاء فاتحة
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(120)
              : const Color(0xFF2563EB).withAlpha(40), // ظل أزرق فاتح
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: isDark
              ? Colors.black.withAlpha(60)
              : const Color(0xFF2563EB).withAlpha(20), // ظل ثانوي
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        color: isDark
            ? Theme.of(context).colorScheme.surface
            : const Color(0xFFF8FAFF), // خلفية زرقاء فاتحة جداً
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ErrorDetailsScreen(errorId: error.id),
              ),
            );
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: EdgeInsets.all(isCompact ? 14 : 18),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isDark ? [
                  Theme.of(context).colorScheme.surface,
                  Theme.of(context).colorScheme.surface.withAlpha(230),
                ] : [
                  const Color(0xFFF8FAFF), // زرقاء فاتحة جداً
                  const Color(0xFFF1F5FF), // زرقاء فاتحة أكثر قليلاً
                  const Color(0xFFEBF2FF), // زرقاء فاتحة أكثر
                ],
                stops: isDark ? null : [0.0, 0.5, 1.0],
              ),
            ),
            child: isCompact ? _buildCompactLayout(context, isRTL) : _buildFullLayout(context, isRTL),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactLayout(BuildContext context, bool isRTL) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '${error.manufacturer} ${error.model}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 6),
        Text(
          isRTL ? 'كود الخطأ: ${error.errorCode}' : 'Error: ${error.errorCode}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.red,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          error.description,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildCategoryChip(context),
            const Spacer(),
            if (showFavoriteButton) ...[
              _buildFavoriteButton(context),
              const SizedBox(width: 8),
            ],
            Text(
              _formatDate(error.createdAt),
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFullLayout(BuildContext context, bool isRTL) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '${error.manufacturer} ${error.model}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 6),
        Text(
          isRTL ? 'كود الخطأ: ${error.errorCode}' : 'Error Code: ${error.errorCode}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.red,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          error.description,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildCategoryChip(context),
            const Spacer(),
            if (showFavoriteButton) ...[
              _buildFavoriteButton(context),
              const SizedBox(width: 8),
            ],
            Text(
              _formatDate(error.createdAt),
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      ],
    );
  }



  Widget _buildCategoryChip(BuildContext context) {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        // Use the new method that provides a proper fallback
        final categoryName = categoryProvider.getCategoryNameById(error.categoryId);

        final isDark = Theme.of(context).brightness == Brightness.dark;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
          decoration: BoxDecoration(
            color: isDark
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.15)
                : const Color(0xFF2563EB).withValues(alpha: 0.12), // زرقاء فاتحة
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: isDark
                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                  : const Color(0xFF2563EB).withValues(alpha: 0.25),
              width: 1,
            ),
          ),
          child: Text(
            categoryName,
            style: (isCompact
                ? Theme.of(context).textTheme.labelSmall
                : Theme.of(context).textTheme.labelMedium)?.copyWith(
              color: isDark
                  ? Theme.of(context).colorScheme.primary
                  : const Color(0xFF2563EB), // نص أزرق
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      },
    );
  }

  Widget _buildFavoriteButton(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onFavoritePressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: isDark
                ? Colors.red.withValues(alpha: 0.15)
                : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.red.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.favorite,
            color: Colors.red,
            size: isCompact ? 16 : 18,
          ),
        ),
      ),
    );
  }



  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
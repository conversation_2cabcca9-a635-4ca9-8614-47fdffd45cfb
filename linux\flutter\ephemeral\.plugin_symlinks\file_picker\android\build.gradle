group 'com.mr.flutter.plugin.filepicker'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdk 34

    defaultConfig {
        minSdk 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    
    dependencies {
        implementation 'androidx.core:core:1.0.2'
        implementation 'androidx.annotation:annotation:1.0.0'
        implementation "androidx.lifecycle:lifecycle-runtime:2.1.0"
    }

    if (project.android.hasProperty("namespace")) {
        namespace 'com.mr.flutter.plugin.filepicker' 
    }
}

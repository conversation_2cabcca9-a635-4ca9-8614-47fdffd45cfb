name: flutter_localization
description: Flutter Localization is a package use for in-app localization with map data. More easier and faster to implement and inspired by the flutter_localizations itself.
version: 0.2.1
homepage: https://github.com/channdara/flutter_localization
issue_tracker: https://github.com/channdara/flutter_localization/issues

scripts:
  # rps test
  test: 'flutter pub publish --dry-run'
  # rps publish
  publish: 'flutter pub publish'

environment:
  sdk: '>=2.12.0 <4.0.0'
  flutter: '>=1.12.13'

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  plugin_platform_interface: ^2.1.8
  shared_preferences: ^2.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0

flutter:
  plugin:
    platforms:
      android:
        package: com.mastertipsy.flutter_localization
        pluginClass: FlutterLocalizationPlugin
      ios:
        pluginClass: FlutterLocalizationPlugin
      web:
        pluginClass: FlutterLocalizationWeb
        fileName: flutter_localization_web.dart
      linux:
        pluginClass: FlutterLocalizationPlugin
      macos:
        pluginClass: FlutterLocalizationPlugin
      windows:
        pluginClass: FlutterLocalizationPluginCApi
// Copyright 2023, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.
// Autogenerated from Pigeon (v11.0.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import "FirestoreMessages.g.h"
#import "FLTFirebaseFirestoreReader.h"
#import "FLTFirebaseFirestoreWriter.h"

#if TARGET_OS_OSX
#import <FlutterMacOS/FlutterMacOS.h>
#else
#import <Flutter/Flutter.h>
#endif

#if !__has_feature(objc_arc)
#error File requires ARC to be enabled.
#endif

/// An enumeration of document change types.
@implementation DocumentChangeTypeBox
- (instancetype)initWithValue:(DocumentChangeType)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

/// An enumeration of firestore source types.
@implementation SourceBox
- (instancetype)initWithValue:(Source)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

/// The listener retrieves data and listens to updates from the local Firestore cache only.
/// If the cache is empty, an empty snapshot will be returned.
/// Snapshot events will be triggered on cache updates, like local mutations or load bundles.
///
/// Note that the data might be stale if the cache hasn't synchronized with recent server-side
/// changes.
@implementation ListenSourceBox
- (instancetype)initWithValue:(ListenSource)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation ServerTimestampBehaviorBox
- (instancetype)initWithValue:(ServerTimestampBehavior)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

/// [AggregateSource] represents the source of data for an [AggregateQuery].
@implementation AggregateSourceBox
- (instancetype)initWithValue:(AggregateSource)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

/// [PersistenceCacheIndexManagerRequest] represents the request types for the persistence cache
/// index manager.
@implementation PersistenceCacheIndexManagerRequestBox
- (instancetype)initWithValue:(PersistenceCacheIndexManagerRequest)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation PigeonTransactionResultBox
- (instancetype)initWithValue:(PigeonTransactionResult)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation PigeonTransactionTypeBox
- (instancetype)initWithValue:(PigeonTransactionType)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation AggregateTypeBox
- (instancetype)initWithValue:(AggregateType)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

static NSArray *wrapResult(id result, FlutterError *error) {
  if (error) {
    return @[
      error.code ?: [NSNull null], error.message ?: [NSNull null], error.details ?: [NSNull null]
    ];
  }
  return @[ result ?: [NSNull null] ];
}
static id GetNullableObjectAtIndex(NSArray *array, NSInteger key) {
  id result = array[key];
  return (result == [NSNull null]) ? nil : result;
}

@interface PigeonFirebaseSettings ()
+ (PigeonFirebaseSettings *)fromList:(NSArray *)list;
+ (nullable PigeonFirebaseSettings *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface FirestorePigeonFirebaseApp ()
+ (FirestorePigeonFirebaseApp *)fromList:(NSArray *)list;
+ (nullable FirestorePigeonFirebaseApp *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonSnapshotMetadata ()
+ (PigeonSnapshotMetadata *)fromList:(NSArray *)list;
+ (nullable PigeonSnapshotMetadata *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonDocumentSnapshot ()
+ (PigeonDocumentSnapshot *)fromList:(NSArray *)list;
+ (nullable PigeonDocumentSnapshot *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonDocumentChange ()
+ (PigeonDocumentChange *)fromList:(NSArray *)list;
+ (nullable PigeonDocumentChange *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonQuerySnapshot ()
+ (PigeonQuerySnapshot *)fromList:(NSArray *)list;
+ (nullable PigeonQuerySnapshot *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonGetOptions ()
+ (PigeonGetOptions *)fromList:(NSArray *)list;
+ (nullable PigeonGetOptions *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonDocumentOption ()
+ (PigeonDocumentOption *)fromList:(NSArray *)list;
+ (nullable PigeonDocumentOption *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonTransactionCommand ()
+ (PigeonTransactionCommand *)fromList:(NSArray *)list;
+ (nullable PigeonTransactionCommand *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface DocumentReferenceRequest ()
+ (DocumentReferenceRequest *)fromList:(NSArray *)list;
+ (nullable DocumentReferenceRequest *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonQueryParameters ()
+ (PigeonQueryParameters *)fromList:(NSArray *)list;
+ (nullable PigeonQueryParameters *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface AggregateQuery ()
+ (AggregateQuery *)fromList:(NSArray *)list;
+ (nullable AggregateQuery *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface AggregateQueryResponse ()
+ (AggregateQueryResponse *)fromList:(NSArray *)list;
+ (nullable AggregateQueryResponse *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@implementation PigeonFirebaseSettings
+ (instancetype)makeWithPersistenceEnabled:(nullable NSNumber *)persistenceEnabled
                                      host:(nullable NSString *)host
                                sslEnabled:(nullable NSNumber *)sslEnabled
                            cacheSizeBytes:(nullable NSNumber *)cacheSizeBytes
                 ignoreUndefinedProperties:(NSNumber *)ignoreUndefinedProperties {
  PigeonFirebaseSettings *pigeonResult = [[PigeonFirebaseSettings alloc] init];
  pigeonResult.persistenceEnabled = persistenceEnabled;
  pigeonResult.host = host;
  pigeonResult.sslEnabled = sslEnabled;
  pigeonResult.cacheSizeBytes = cacheSizeBytes;
  pigeonResult.ignoreUndefinedProperties = ignoreUndefinedProperties;
  return pigeonResult;
}
+ (PigeonFirebaseSettings *)fromList:(NSArray *)list {
  PigeonFirebaseSettings *pigeonResult = [[PigeonFirebaseSettings alloc] init];
  pigeonResult.persistenceEnabled = GetNullableObjectAtIndex(list, 0);
  pigeonResult.host = GetNullableObjectAtIndex(list, 1);
  pigeonResult.sslEnabled = GetNullableObjectAtIndex(list, 2);
  pigeonResult.cacheSizeBytes = GetNullableObjectAtIndex(list, 3);
  pigeonResult.ignoreUndefinedProperties = GetNullableObjectAtIndex(list, 4);
  NSAssert(pigeonResult.ignoreUndefinedProperties != nil, @"");
  return pigeonResult;
}
+ (nullable PigeonFirebaseSettings *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonFirebaseSettings fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.persistenceEnabled ?: [NSNull null]),
    (self.host ?: [NSNull null]),
    (self.sslEnabled ?: [NSNull null]),
    (self.cacheSizeBytes ?: [NSNull null]),
    (self.ignoreUndefinedProperties ?: [NSNull null]),
  ];
}
@end

@implementation FirestorePigeonFirebaseApp
+ (instancetype)makeWithAppName:(NSString *)appName
                       settings:(PigeonFirebaseSettings *)settings
                    databaseURL:(NSString *)databaseURL {
  FirestorePigeonFirebaseApp *pigeonResult = [[FirestorePigeonFirebaseApp alloc] init];
  pigeonResult.appName = appName;
  pigeonResult.settings = settings;
  pigeonResult.databaseURL = databaseURL;
  return pigeonResult;
}
+ (FirestorePigeonFirebaseApp *)fromList:(NSArray *)list {
  FirestorePigeonFirebaseApp *pigeonResult = [[FirestorePigeonFirebaseApp alloc] init];
  pigeonResult.appName = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.appName != nil, @"");
  pigeonResult.settings =
      [PigeonFirebaseSettings nullableFromList:(GetNullableObjectAtIndex(list, 1))];
  NSAssert(pigeonResult.settings != nil, @"");
  pigeonResult.databaseURL = GetNullableObjectAtIndex(list, 2);
  NSAssert(pigeonResult.databaseURL != nil, @"");
  return pigeonResult;
}
+ (nullable FirestorePigeonFirebaseApp *)nullableFromList:(NSArray *)list {
  return (list) ? [FirestorePigeonFirebaseApp fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.appName ?: [NSNull null]),
    (self.settings ? [self.settings toList] : [NSNull null]),
    (self.databaseURL ?: [NSNull null]),
  ];
}
@end

@implementation PigeonSnapshotMetadata
+ (instancetype)makeWithHasPendingWrites:(NSNumber *)hasPendingWrites
                             isFromCache:(NSNumber *)isFromCache {
  PigeonSnapshotMetadata *pigeonResult = [[PigeonSnapshotMetadata alloc] init];
  pigeonResult.hasPendingWrites = hasPendingWrites;
  pigeonResult.isFromCache = isFromCache;
  return pigeonResult;
}
+ (PigeonSnapshotMetadata *)fromList:(NSArray *)list {
  PigeonSnapshotMetadata *pigeonResult = [[PigeonSnapshotMetadata alloc] init];
  pigeonResult.hasPendingWrites = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.hasPendingWrites != nil, @"");
  pigeonResult.isFromCache = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.isFromCache != nil, @"");
  return pigeonResult;
}
+ (nullable PigeonSnapshotMetadata *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonSnapshotMetadata fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.hasPendingWrites ?: [NSNull null]),
    (self.isFromCache ?: [NSNull null]),
  ];
}
@end

@implementation PigeonDocumentSnapshot
+ (instancetype)makeWithPath:(NSString *)path
                        data:(nullable NSDictionary<NSString *, id> *)data
                    metadata:(PigeonSnapshotMetadata *)metadata {
  PigeonDocumentSnapshot *pigeonResult = [[PigeonDocumentSnapshot alloc] init];
  pigeonResult.path = path;
  pigeonResult.data = data;
  pigeonResult.metadata = metadata;
  return pigeonResult;
}
+ (PigeonDocumentSnapshot *)fromList:(NSArray *)list {
  PigeonDocumentSnapshot *pigeonResult = [[PigeonDocumentSnapshot alloc] init];
  pigeonResult.path = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.path != nil, @"");
  pigeonResult.data = GetNullableObjectAtIndex(list, 1);
  pigeonResult.metadata =
      [PigeonSnapshotMetadata nullableFromList:(GetNullableObjectAtIndex(list, 2))];
  NSAssert(pigeonResult.metadata != nil, @"");
  return pigeonResult;
}
+ (nullable PigeonDocumentSnapshot *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonDocumentSnapshot fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.path ?: [NSNull null]),
    (self.data ?: [NSNull null]),
    (self.metadata ? [self.metadata toList] : [NSNull null]),
  ];
}
@end

@implementation PigeonDocumentChange
+ (instancetype)makeWithType:(DocumentChangeType)type
                    document:(PigeonDocumentSnapshot *)document
                    oldIndex:(NSNumber *)oldIndex
                    newIndex:(NSNumber *)newIndex {
  PigeonDocumentChange *pigeonResult = [[PigeonDocumentChange alloc] init];
  pigeonResult.type = type;
  pigeonResult.document = document;
  pigeonResult.oldIndex = oldIndex;
  pigeonResult.index = newIndex;
  return pigeonResult;
}
+ (PigeonDocumentChange *)fromList:(NSArray *)list {
  PigeonDocumentChange *pigeonResult = [[PigeonDocumentChange alloc] init];
  pigeonResult.type = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.document =
      [PigeonDocumentSnapshot nullableFromList:(GetNullableObjectAtIndex(list, 1))];
  NSAssert(pigeonResult.document != nil, @"");
  pigeonResult.oldIndex = GetNullableObjectAtIndex(list, 2);
  NSAssert(pigeonResult.oldIndex != nil, @"");
  pigeonResult.index = GetNullableObjectAtIndex(list, 3);
  NSAssert(pigeonResult.index != nil, @"");
  return pigeonResult;
}
+ (nullable PigeonDocumentChange *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonDocumentChange fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.type),
    (self.document ? [self.document toList] : [NSNull null]),
    (self.oldIndex ?: [NSNull null]),
    (self.index ?: [NSNull null]),
  ];
}
@end

@implementation PigeonQuerySnapshot
+ (instancetype)makeWithDocuments:(NSArray<PigeonDocumentSnapshot *> *)documents
                  documentChanges:(NSArray<PigeonDocumentChange *> *)documentChanges
                         metadata:(PigeonSnapshotMetadata *)metadata {
  PigeonQuerySnapshot *pigeonResult = [[PigeonQuerySnapshot alloc] init];
  pigeonResult.documents = documents;
  pigeonResult.documentChanges = documentChanges;
  pigeonResult.metadata = metadata;
  return pigeonResult;
}
+ (PigeonQuerySnapshot *)fromList:(NSArray *)list {
  PigeonQuerySnapshot *pigeonResult = [[PigeonQuerySnapshot alloc] init];
  pigeonResult.documents = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.documents != nil, @"");
  pigeonResult.documentChanges = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.documentChanges != nil, @"");
  pigeonResult.metadata =
      [PigeonSnapshotMetadata nullableFromList:(GetNullableObjectAtIndex(list, 2))];
  NSAssert(pigeonResult.metadata != nil, @"");
  return pigeonResult;
}
+ (nullable PigeonQuerySnapshot *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonQuerySnapshot fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.documents ?: [NSNull null]),
    (self.documentChanges ?: [NSNull null]),
    (self.metadata ? [self.metadata toList] : [NSNull null]),
  ];
}
@end

@implementation PigeonGetOptions
+ (instancetype)makeWithSource:(Source)source
       serverTimestampBehavior:(ServerTimestampBehavior)serverTimestampBehavior {
  PigeonGetOptions *pigeonResult = [[PigeonGetOptions alloc] init];
  pigeonResult.source = source;
  pigeonResult.serverTimestampBehavior = serverTimestampBehavior;
  return pigeonResult;
}
+ (PigeonGetOptions *)fromList:(NSArray *)list {
  PigeonGetOptions *pigeonResult = [[PigeonGetOptions alloc] init];
  pigeonResult.source = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.serverTimestampBehavior = [GetNullableObjectAtIndex(list, 1) integerValue];
  return pigeonResult;
}
+ (nullable PigeonGetOptions *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonGetOptions fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.source),
    @(self.serverTimestampBehavior),
  ];
}
@end

@implementation PigeonDocumentOption
+ (instancetype)makeWithMerge:(nullable NSNumber *)merge
                  mergeFields:(nullable NSArray<NSArray<NSString *> *> *)mergeFields {
  PigeonDocumentOption *pigeonResult = [[PigeonDocumentOption alloc] init];
  pigeonResult.merge = merge;
  pigeonResult.mergeFields = mergeFields;
  return pigeonResult;
}
+ (PigeonDocumentOption *)fromList:(NSArray *)list {
  PigeonDocumentOption *pigeonResult = [[PigeonDocumentOption alloc] init];
  pigeonResult.merge = GetNullableObjectAtIndex(list, 0);
  pigeonResult.mergeFields = GetNullableObjectAtIndex(list, 1);
  return pigeonResult;
}
+ (nullable PigeonDocumentOption *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonDocumentOption fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.merge ?: [NSNull null]),
    (self.mergeFields ?: [NSNull null]),
  ];
}
@end

@implementation PigeonTransactionCommand
+ (instancetype)makeWithType:(PigeonTransactionType)type
                        path:(NSString *)path
                        data:(nullable NSDictionary<NSString *, id> *)data
                      option:(nullable PigeonDocumentOption *)option {
  PigeonTransactionCommand *pigeonResult = [[PigeonTransactionCommand alloc] init];
  pigeonResult.type = type;
  pigeonResult.path = path;
  pigeonResult.data = data;
  pigeonResult.option = option;
  return pigeonResult;
}
+ (PigeonTransactionCommand *)fromList:(NSArray *)list {
  PigeonTransactionCommand *pigeonResult = [[PigeonTransactionCommand alloc] init];
  pigeonResult.type = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.path = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.path != nil, @"");
  pigeonResult.data = GetNullableObjectAtIndex(list, 2);
  pigeonResult.option = [PigeonDocumentOption nullableFromList:(GetNullableObjectAtIndex(list, 3))];
  return pigeonResult;
}
+ (nullable PigeonTransactionCommand *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonTransactionCommand fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.type),
    (self.path ?: [NSNull null]),
    (self.data ?: [NSNull null]),
    (self.option ? [self.option toList] : [NSNull null]),
  ];
}
@end

@implementation DocumentReferenceRequest
+ (instancetype)makeWithPath:(NSString *)path
                        data:(nullable NSDictionary<id, id> *)data
                      option:(nullable PigeonDocumentOption *)option
                      source:(nullable SourceBox *)source
     serverTimestampBehavior:(nullable ServerTimestampBehaviorBox *)serverTimestampBehavior {
  DocumentReferenceRequest *pigeonResult = [[DocumentReferenceRequest alloc] init];
  pigeonResult.path = path;
  pigeonResult.data = data;
  pigeonResult.option = option;
  pigeonResult.source = source;
  pigeonResult.serverTimestampBehavior = serverTimestampBehavior;
  return pigeonResult;
}
+ (DocumentReferenceRequest *)fromList:(NSArray *)list {
  DocumentReferenceRequest *pigeonResult = [[DocumentReferenceRequest alloc] init];
  pigeonResult.path = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.path != nil, @"");
  pigeonResult.data = GetNullableObjectAtIndex(list, 1);
  pigeonResult.option = [PigeonDocumentOption nullableFromList:(GetNullableObjectAtIndex(list, 2))];
  NSNumber *sourceAsNumber = GetNullableObjectAtIndex(list, 3);
  SourceBox *source =
      sourceAsNumber == nil ? nil : [[SourceBox alloc] initWithValue:[sourceAsNumber integerValue]];
  pigeonResult.source = source;
  NSNumber *serverTimestampBehaviorAsNumber = GetNullableObjectAtIndex(list, 4);
  ServerTimestampBehaviorBox *serverTimestampBehavior =
      serverTimestampBehaviorAsNumber == nil
          ? nil
          : [[ServerTimestampBehaviorBox alloc]
                initWithValue:[serverTimestampBehaviorAsNumber integerValue]];
  pigeonResult.serverTimestampBehavior = serverTimestampBehavior;
  return pigeonResult;
}
+ (nullable DocumentReferenceRequest *)nullableFromList:(NSArray *)list {
  return (list) ? [DocumentReferenceRequest fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.path ?: [NSNull null]),
    (self.data ?: [NSNull null]),
    (self.option ? [self.option toList] : [NSNull null]),
    (self.source == nil ? [NSNull null] : [NSNumber numberWithInteger:self.source.value]),
    (self.serverTimestampBehavior == nil
         ? [NSNull null]
         : [NSNumber numberWithInteger:self.serverTimestampBehavior.value]),
  ];
}
@end

@implementation PigeonQueryParameters
+ (instancetype)makeWithWhere:(nullable NSArray<NSArray<id> *> *)where
                      orderBy:(nullable NSArray<NSArray<id> *> *)orderBy
                        limit:(nullable NSNumber *)limit
                  limitToLast:(nullable NSNumber *)limitToLast
                      startAt:(nullable NSArray<id> *)startAt
                   startAfter:(nullable NSArray<id> *)startAfter
                        endAt:(nullable NSArray<id> *)endAt
                    endBefore:(nullable NSArray<id> *)endBefore
                      filters:(nullable NSDictionary<NSString *, id> *)filters {
  PigeonQueryParameters *pigeonResult = [[PigeonQueryParameters alloc] init];
  pigeonResult.where = where;
  pigeonResult.orderBy = orderBy;
  pigeonResult.limit = limit;
  pigeonResult.limitToLast = limitToLast;
  pigeonResult.startAt = startAt;
  pigeonResult.startAfter = startAfter;
  pigeonResult.endAt = endAt;
  pigeonResult.endBefore = endBefore;
  pigeonResult.filters = filters;
  return pigeonResult;
}
+ (PigeonQueryParameters *)fromList:(NSArray *)list {
  PigeonQueryParameters *pigeonResult = [[PigeonQueryParameters alloc] init];
  pigeonResult.where = GetNullableObjectAtIndex(list, 0);
  pigeonResult.orderBy = GetNullableObjectAtIndex(list, 1);
  pigeonResult.limit = GetNullableObjectAtIndex(list, 2);
  pigeonResult.limitToLast = GetNullableObjectAtIndex(list, 3);
  pigeonResult.startAt = GetNullableObjectAtIndex(list, 4);
  pigeonResult.startAfter = GetNullableObjectAtIndex(list, 5);
  pigeonResult.endAt = GetNullableObjectAtIndex(list, 6);
  pigeonResult.endBefore = GetNullableObjectAtIndex(list, 7);
  pigeonResult.filters = GetNullableObjectAtIndex(list, 8);
  return pigeonResult;
}
+ (nullable PigeonQueryParameters *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonQueryParameters fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.where ?: [NSNull null]),
    (self.orderBy ?: [NSNull null]),
    (self.limit ?: [NSNull null]),
    (self.limitToLast ?: [NSNull null]),
    (self.startAt ?: [NSNull null]),
    (self.startAfter ?: [NSNull null]),
    (self.endAt ?: [NSNull null]),
    (self.endBefore ?: [NSNull null]),
    (self.filters ?: [NSNull null]),
  ];
}
@end

@implementation AggregateQuery
+ (instancetype)makeWithType:(AggregateType)type field:(nullable NSString *)field {
  AggregateQuery *pigeonResult = [[AggregateQuery alloc] init];
  pigeonResult.type = type;
  pigeonResult.field = field;
  return pigeonResult;
}
+ (AggregateQuery *)fromList:(NSArray *)list {
  AggregateQuery *pigeonResult = [[AggregateQuery alloc] init];
  pigeonResult.type = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.field = GetNullableObjectAtIndex(list, 1);
  return pigeonResult;
}
+ (nullable AggregateQuery *)nullableFromList:(NSArray *)list {
  return (list) ? [AggregateQuery fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.type),
    (self.field ?: [NSNull null]),
  ];
}
@end

@implementation AggregateQueryResponse
+ (instancetype)makeWithType:(AggregateType)type
                       field:(nullable NSString *)field
                       value:(nullable NSNumber *)value {
  AggregateQueryResponse *pigeonResult = [[AggregateQueryResponse alloc] init];
  pigeonResult.type = type;
  pigeonResult.field = field;
  pigeonResult.value = value;
  return pigeonResult;
}
+ (AggregateQueryResponse *)fromList:(NSArray *)list {
  AggregateQueryResponse *pigeonResult = [[AggregateQueryResponse alloc] init];
  pigeonResult.type = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.field = GetNullableObjectAtIndex(list, 1);
  pigeonResult.value = GetNullableObjectAtIndex(list, 2);
  return pigeonResult;
}
+ (nullable AggregateQueryResponse *)nullableFromList:(NSArray *)list {
  return (list) ? [AggregateQueryResponse fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.type),
    (self.field ?: [NSNull null]),
    (self.value ?: [NSNull null]),
  ];
}
@end

@interface FirebaseFirestoreHostApiCodecReader : FLTFirebaseFirestoreReader
@end
@implementation FirebaseFirestoreHostApiCodecReader
- (nullable id)readValueOfType:(UInt8)type {
  switch (type) {
    case 128:
      return [AggregateQuery fromList:[self readValue]];
    case 129:
      return [AggregateQueryResponse fromList:[self readValue]];
    case 130:
      return [DocumentReferenceRequest fromList:[self readValue]];
    case 131:
      return [FirestorePigeonFirebaseApp fromList:[self readValue]];
    case 132:
      return [PigeonDocumentChange fromList:[self readValue]];
    case 133:
      return [PigeonDocumentOption fromList:[self readValue]];
    case 134:
      return [PigeonDocumentSnapshot fromList:[self readValue]];
    case 135:
      return [PigeonFirebaseSettings fromList:[self readValue]];
    case 136:
      return [PigeonGetOptions fromList:[self readValue]];
    case 137:
      return [PigeonQueryParameters fromList:[self readValue]];
    case 138:
      return [PigeonQuerySnapshot fromList:[self readValue]];
    case 139:
      return [PigeonSnapshotMetadata fromList:[self readValue]];
    case 140:
      return [PigeonTransactionCommand fromList:[self readValue]];
    default:
      return [super readValueOfType:type];
  }
}
@end

@interface FirebaseFirestoreHostApiCodecWriter : FLTFirebaseFirestoreWriter
@end
@implementation FirebaseFirestoreHostApiCodecWriter
- (void)writeValue:(id)value {
  if ([value isKindOfClass:[AggregateQuery class]]) {
    [self writeByte:128];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[AggregateQueryResponse class]]) {
    [self writeByte:129];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[DocumentReferenceRequest class]]) {
    [self writeByte:130];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[FirestorePigeonFirebaseApp class]]) {
    [self writeByte:131];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonDocumentChange class]]) {
    [self writeByte:132];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonDocumentOption class]]) {
    [self writeByte:133];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonDocumentSnapshot class]]) {
    [self writeByte:134];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonFirebaseSettings class]]) {
    [self writeByte:135];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonGetOptions class]]) {
    [self writeByte:136];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonQueryParameters class]]) {
    [self writeByte:137];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonQuerySnapshot class]]) {
    [self writeByte:138];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonSnapshotMetadata class]]) {
    [self writeByte:139];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonTransactionCommand class]]) {
    [self writeByte:140];
    [self writeValue:[value toList]];
  } else {
    [super writeValue:value];
  }
}
@end

@interface FirebaseFirestoreHostApiCodecReaderWriter : FlutterStandardReaderWriter
@end
@implementation FirebaseFirestoreHostApiCodecReaderWriter
- (FlutterStandardWriter *)writerWithData:(NSMutableData *)data {
  return [[FirebaseFirestoreHostApiCodecWriter alloc] initWithData:data];
}
- (FlutterStandardReader *)readerWithData:(NSData *)data {
  return [[FirebaseFirestoreHostApiCodecReader alloc] initWithData:data];
}
@end

NSObject<FlutterMessageCodec> *FirebaseFirestoreHostApiGetCodec(void) {
  static FlutterStandardMessageCodec *sSharedObject = nil;
  static dispatch_once_t sPred = 0;
  dispatch_once(&sPred, ^{
    FirebaseFirestoreHostApiCodecReaderWriter *readerWriter =
        [[FirebaseFirestoreHostApiCodecReaderWriter alloc] init];
    sSharedObject = [FlutterStandardMessageCodec codecWithReaderWriter:readerWriter];
  });
  return sSharedObject;
}

void FirebaseFirestoreHostApiSetup(id<FlutterBinaryMessenger> binaryMessenger,
                                   NSObject<FirebaseFirestoreHostApi> *api) {
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.loadBundle"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(loadBundleApp:bundle:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(loadBundleApp:bundle:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        FlutterStandardTypedData *arg_bundle = GetNullableObjectAtIndex(args, 1);
        [api loadBundleApp:arg_app
                    bundle:arg_bundle
                completion:^(NSString *_Nullable output, FlutterError *_Nullable error) {
                  callback(wrapResult(output, error));
                }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.namedQueryGet"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(namedQueryGetApp:name:options:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(namedQueryGetApp:name:options:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSString *arg_name = GetNullableObjectAtIndex(args, 1);
        PigeonGetOptions *arg_options = GetNullableObjectAtIndex(args, 2);
        [api namedQueryGetApp:arg_app
                         name:arg_name
                      options:arg_options
                   completion:^(PigeonQuerySnapshot *_Nullable output,
                                FlutterError *_Nullable error) {
                     callback(wrapResult(output, error));
                   }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.clearPersistence"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(clearPersistenceApp:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(clearPersistenceApp:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        [api clearPersistenceApp:arg_app
                      completion:^(FlutterError *_Nullable error) {
                        callback(wrapResult(nil, error));
                      }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.disableNetwork"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(disableNetworkApp:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(disableNetworkApp:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        [api disableNetworkApp:arg_app
                    completion:^(FlutterError *_Nullable error) {
                      callback(wrapResult(nil, error));
                    }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.enableNetwork"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(enableNetworkApp:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(enableNetworkApp:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        [api enableNetworkApp:arg_app
                   completion:^(FlutterError *_Nullable error) {
                     callback(wrapResult(nil, error));
                   }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.terminate"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(terminateApp:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(terminateApp:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        [api terminateApp:arg_app
               completion:^(FlutterError *_Nullable error) {
                 callback(wrapResult(nil, error));
               }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.waitForPendingWrites"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(waitForPendingWritesApp:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(waitForPendingWritesApp:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        [api waitForPendingWritesApp:arg_app
                          completion:^(FlutterError *_Nullable error) {
                            callback(wrapResult(nil, error));
                          }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.setIndexConfiguration"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setIndexConfigurationApp:
                                                        indexConfiguration:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(setIndexConfigurationApp:indexConfiguration:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSString *arg_indexConfiguration = GetNullableObjectAtIndex(args, 1);
        [api setIndexConfigurationApp:arg_app
                   indexConfiguration:arg_indexConfiguration
                           completion:^(FlutterError *_Nullable error) {
                             callback(wrapResult(nil, error));
                           }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.setLoggingEnabled"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setLoggingEnabledLoggingEnabled:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(setLoggingEnabledLoggingEnabled:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_loggingEnabled = GetNullableObjectAtIndex(args, 0);
        [api setLoggingEnabledLoggingEnabled:arg_loggingEnabled
                                  completion:^(FlutterError *_Nullable error) {
                                    callback(wrapResult(nil, error));
                                  }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.snapshotsInSyncSetup"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(snapshotsInSyncSetupApp:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(snapshotsInSyncSetupApp:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        [api snapshotsInSyncSetupApp:arg_app
                          completion:^(NSString *_Nullable output, FlutterError *_Nullable error) {
                            callback(wrapResult(output, error));
                          }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.transactionCreate"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(transactionCreateApp:
                                                               timeout:maxAttempts:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(transactionCreateApp:timeout:maxAttempts:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSNumber *arg_timeout = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_maxAttempts = GetNullableObjectAtIndex(args, 2);
        [api transactionCreateApp:arg_app
                          timeout:arg_timeout
                      maxAttempts:arg_maxAttempts
                       completion:^(NSString *_Nullable output, FlutterError *_Nullable error) {
                         callback(wrapResult(output, error));
                       }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.transactionStoreResult"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector
                     (transactionStoreResultTransactionId:resultType:commands:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(transactionStoreResultTransactionId:resultType:commands:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_transactionId = GetNullableObjectAtIndex(args, 0);
        PigeonTransactionResult arg_resultType = [GetNullableObjectAtIndex(args, 1) integerValue];
        NSArray<PigeonTransactionCommand *> *arg_commands = GetNullableObjectAtIndex(args, 2);
        [api transactionStoreResultTransactionId:arg_transactionId
                                      resultType:arg_resultType
                                        commands:arg_commands
                                      completion:^(FlutterError *_Nullable error) {
                                        callback(wrapResult(nil, error));
                                      }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.transactionGet"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(transactionGetApp:
                                                      transactionId:path:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(transactionGetApp:transactionId:path:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSString *arg_transactionId = GetNullableObjectAtIndex(args, 1);
        NSString *arg_path = GetNullableObjectAtIndex(args, 2);
        [api transactionGetApp:arg_app
                 transactionId:arg_transactionId
                          path:arg_path
                    completion:^(PigeonDocumentSnapshot *_Nullable output,
                                 FlutterError *_Nullable error) {
                      callback(wrapResult(output, error));
                    }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.documentReferenceSet"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(documentReferenceSetApp:request:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(documentReferenceSetApp:request:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        DocumentReferenceRequest *arg_request = GetNullableObjectAtIndex(args, 1);
        [api documentReferenceSetApp:arg_app
                             request:arg_request
                          completion:^(FlutterError *_Nullable error) {
                            callback(wrapResult(nil, error));
                          }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.documentReferenceUpdate"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(documentReferenceUpdateApp:request:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(documentReferenceUpdateApp:request:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        DocumentReferenceRequest *arg_request = GetNullableObjectAtIndex(args, 1);
        [api documentReferenceUpdateApp:arg_app
                                request:arg_request
                             completion:^(FlutterError *_Nullable error) {
                               callback(wrapResult(nil, error));
                             }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.documentReferenceGet"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(documentReferenceGetApp:request:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(documentReferenceGetApp:request:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        DocumentReferenceRequest *arg_request = GetNullableObjectAtIndex(args, 1);
        [api documentReferenceGetApp:arg_app
                             request:arg_request
                          completion:^(PigeonDocumentSnapshot *_Nullable output,
                                       FlutterError *_Nullable error) {
                            callback(wrapResult(output, error));
                          }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.documentReferenceDelete"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(documentReferenceDeleteApp:request:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(documentReferenceDeleteApp:request:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        DocumentReferenceRequest *arg_request = GetNullableObjectAtIndex(args, 1);
        [api documentReferenceDeleteApp:arg_app
                                request:arg_request
                             completion:^(FlutterError *_Nullable error) {
                               callback(wrapResult(nil, error));
                             }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.queryGet"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector
                     (queryGetApp:path:isCollectionGroup:parameters:options:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(queryGetApp:path:isCollectionGroup:parameters:options:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSString *arg_path = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_isCollectionGroup = GetNullableObjectAtIndex(args, 2);
        PigeonQueryParameters *arg_parameters = GetNullableObjectAtIndex(args, 3);
        PigeonGetOptions *arg_options = GetNullableObjectAtIndex(args, 4);
        [api queryGetApp:arg_app
                         path:arg_path
            isCollectionGroup:arg_isCollectionGroup
                   parameters:arg_parameters
                      options:arg_options
                   completion:^(PigeonQuerySnapshot *_Nullable output,
                                FlutterError *_Nullable error) {
                     callback(wrapResult(output, error));
                   }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.aggregateQuery"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector
                     (aggregateQueryApp:
                                   path:parameters:source:queries:isCollectionGroup:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(aggregateQueryApp:path:parameters:source:queries:isCollectionGroup:"
                @"completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSString *arg_path = GetNullableObjectAtIndex(args, 1);
        PigeonQueryParameters *arg_parameters = GetNullableObjectAtIndex(args, 2);
        AggregateSource arg_source = [GetNullableObjectAtIndex(args, 3) integerValue];
        NSArray<AggregateQuery *> *arg_queries = GetNullableObjectAtIndex(args, 4);
        NSNumber *arg_isCollectionGroup = GetNullableObjectAtIndex(args, 5);
        [api aggregateQueryApp:arg_app
                          path:arg_path
                    parameters:arg_parameters
                        source:arg_source
                       queries:arg_queries
             isCollectionGroup:arg_isCollectionGroup
                    completion:^(NSArray<AggregateQueryResponse *> *_Nullable output,
                                 FlutterError *_Nullable error) {
                      callback(wrapResult(output, error));
                    }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.writeBatchCommit"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(writeBatchCommitApp:writes:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(writeBatchCommitApp:writes:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSArray<PigeonTransactionCommand *> *arg_writes = GetNullableObjectAtIndex(args, 1);
        [api writeBatchCommitApp:arg_app
                          writes:arg_writes
                      completion:^(FlutterError *_Nullable error) {
                        callback(wrapResult(nil, error));
                      }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.querySnapshot"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector
                     (querySnapshotApp:
                                  path:isCollectionGroup:parameters:options:includeMetadataChanges
                                      :source:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(querySnapshotApp:path:isCollectionGroup:parameters:options:"
                @"includeMetadataChanges:source:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        NSString *arg_path = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_isCollectionGroup = GetNullableObjectAtIndex(args, 2);
        PigeonQueryParameters *arg_parameters = GetNullableObjectAtIndex(args, 3);
        PigeonGetOptions *arg_options = GetNullableObjectAtIndex(args, 4);
        NSNumber *arg_includeMetadataChanges = GetNullableObjectAtIndex(args, 5);
        ListenSource arg_source = [GetNullableObjectAtIndex(args, 6) integerValue];
        [api querySnapshotApp:arg_app
                              path:arg_path
                 isCollectionGroup:arg_isCollectionGroup
                        parameters:arg_parameters
                           options:arg_options
            includeMetadataChanges:arg_includeMetadataChanges
                            source:arg_source
                        completion:^(NSString *_Nullable output, FlutterError *_Nullable error) {
                          callback(wrapResult(output, error));
                        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.documentReferenceSnapshot"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector
                     (documentReferenceSnapshotApp:
                                        parameters:includeMetadataChanges:source:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(documentReferenceSnapshotApp:parameters:includeMetadataChanges:source:"
                @"completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        DocumentReferenceRequest *arg_parameters = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_includeMetadataChanges = GetNullableObjectAtIndex(args, 2);
        ListenSource arg_source = [GetNullableObjectAtIndex(args, 3) integerValue];
        [api documentReferenceSnapshotApp:arg_app
                               parameters:arg_parameters
                   includeMetadataChanges:arg_includeMetadataChanges
                                   source:arg_source
                               completion:^(NSString *_Nullable output,
                                            FlutterError *_Nullable error) {
                                 callback(wrapResult(output, error));
                               }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel = [[FlutterBasicMessageChannel alloc]
           initWithName:@"dev.flutter.pigeon.cloud_firestore_platform_interface."
                        @"FirebaseFirestoreHostApi.persistenceCacheIndexManagerRequest"
        binaryMessenger:binaryMessenger
                  codec:FirebaseFirestoreHostApiGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector
                     (persistenceCacheIndexManagerRequestApp:request:completion:)],
                @"FirebaseFirestoreHostApi api (%@) doesn't respond to "
                @"@selector(persistenceCacheIndexManagerRequestApp:request:completion:)",
                api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        FirestorePigeonFirebaseApp *arg_app = GetNullableObjectAtIndex(args, 0);
        PersistenceCacheIndexManagerRequest arg_request =
            [GetNullableObjectAtIndex(args, 1) integerValue];
        [api persistenceCacheIndexManagerRequestApp:arg_app
                                            request:arg_request
                                         completion:^(FlutterError *_Nullable error) {
                                           callback(wrapResult(nil, error));
                                         }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}

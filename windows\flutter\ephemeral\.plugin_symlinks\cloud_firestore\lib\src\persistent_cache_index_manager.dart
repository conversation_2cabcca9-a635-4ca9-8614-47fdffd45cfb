// Copyright 2024, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

part of '../cloud_firestore.dart';

class PersistentCacheIndexManager {
  PersistentCacheIndexManager._(this._delegate) {
    PersistentCacheIndexManagerPlatform.verify(_delegate);
  }

  /// The platform delegate that interacts with the platform code.
  final PersistentCacheIndexManagerPlatform _delegate;

  /// Enables the SDK to create persistent cache indexes automatically for local query
  /// execution when the SDK believes cache indexes can help improves performance.
  /// This feature is disabled by default.
  Future<void> enableIndexAutoCreation() {
    return _delegate.enableIndexAutoCreation();
  }

  /// Stops creating persistent cache indexes automatically for local query execution.
  /// The indexes which have been created by calling `enableIndexAutoCreation()` still take effect.
  Future<void> disableIndexAutoCreation() {
    return _delegate.disableIndexAutoCreation();
  }

  /// Removes all persistent cache indexes. Note this function also deletes indexes
  /// generated by `setIndexConfiguration()`, which is deprecated.
  Future<void> deleteAllIndexes() {
    return _delegate.deleteAllIndexes();
  }
}

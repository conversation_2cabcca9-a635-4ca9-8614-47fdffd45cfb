name: cloud_firestore
description:
  Flutter plugin for Cloud Firestore, a cloud-hosted, noSQL database with
  live synchronization and offline support on Android and iOS.
homepage: https://firebase.google.com/docs/firestore
repository: https://github.com/firebase/flutterfire/tree/main/packages/cloud_firestore/cloud_firestore
version: 5.6.8
topics:
  - firebase
  - firestore
  - realtime
  - database

false_secrets:
  - example/**
  - dartpad/**

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: '>=3.3.0'

dependencies:
  cloud_firestore_platform_interface: ^6.6.8
  cloud_firestore_web: ^4.4.8
  collection: ^1.0.0
  firebase_core: ^3.13.1
  firebase_core_platform_interface: ^5.4.0
  flutter:
    sdk: flutter
  meta: ^1.8.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0

flutter:
  plugin:
    platforms:
      android:
        package: io.flutter.plugins.firebase.firestore
        pluginClass: FlutterFirebaseFirestorePlugin
      ios:
        pluginClass: FLTFirebaseFirestorePlugin
      macos:
        pluginClass: FLTFirebaseFirestorePlugin
      web:
        default_package: cloud_firestore_web
      windows:
        pluginClass: CloudFirestorePluginCApi


// Copyright 2023, the Chromium project authors.  Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.
// Autogenerated from Pig<PERSON> (v11.0.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#undef _HAS_EXCEPTIONS

#include "messages.g.h"

#include <flutter/basic_message_channel.h>
#include <flutter/binary_messenger.h>
#include <flutter/encodable_value.h>
#include <flutter/standard_message_codec.h>

#include <map>
#include <optional>
#include <string>

namespace cloud_firestore_windows {
using flutter::BasicMessageChannel;
using flutter::CustomEncodableValue;
using flutter::EncodableList;
using flutter::EncodableMap;
using flutter::EncodableValue;

// PigeonFirebaseSettings

PigeonFirebaseSettings::PigeonFirebaseSettings(bool ignore_undefined_properties)
    : ignore_undefined_properties_(ignore_undefined_properties) {}

PigeonFirebaseSettings::PigeonFirebaseSettings(const bool* persistence_enabled,
                                               const std::string* host,
                                               const bool* ssl_enabled,
                                               const int64_t* cache_size_bytes,
                                               bool ignore_undefined_properties)
    : persistence_enabled_(persistence_enabled
                               ? std::optional<bool>(*persistence_enabled)
                               : std::nullopt),
      host_(host ? std::optional<std::string>(*host) : std::nullopt),
      ssl_enabled_(ssl_enabled ? std::optional<bool>(*ssl_enabled)
                               : std::nullopt),
      cache_size_bytes_(cache_size_bytes
                            ? std::optional<int64_t>(*cache_size_bytes)
                            : std::nullopt),
      ignore_undefined_properties_(ignore_undefined_properties) {}

const bool* PigeonFirebaseSettings::persistence_enabled() const {
  return persistence_enabled_ ? &(*persistence_enabled_) : nullptr;
}

void PigeonFirebaseSettings::set_persistence_enabled(const bool* value_arg) {
  persistence_enabled_ =
      value_arg ? std::optional<bool>(*value_arg) : std::nullopt;
}

void PigeonFirebaseSettings::set_persistence_enabled(bool value_arg) {
  persistence_enabled_ = value_arg;
}

const std::string* PigeonFirebaseSettings::host() const {
  return host_ ? &(*host_) : nullptr;
}

void PigeonFirebaseSettings::set_host(const std::string_view* value_arg) {
  host_ = value_arg ? std::optional<std::string>(*value_arg) : std::nullopt;
}

void PigeonFirebaseSettings::set_host(std::string_view value_arg) {
  host_ = value_arg;
}

const bool* PigeonFirebaseSettings::ssl_enabled() const {
  return ssl_enabled_ ? &(*ssl_enabled_) : nullptr;
}

void PigeonFirebaseSettings::set_ssl_enabled(const bool* value_arg) {
  ssl_enabled_ = value_arg ? std::optional<bool>(*value_arg) : std::nullopt;
}

void PigeonFirebaseSettings::set_ssl_enabled(bool value_arg) {
  ssl_enabled_ = value_arg;
}

const int64_t* PigeonFirebaseSettings::cache_size_bytes() const {
  return cache_size_bytes_ ? &(*cache_size_bytes_) : nullptr;
}

void PigeonFirebaseSettings::set_cache_size_bytes(const int64_t* value_arg) {
  cache_size_bytes_ =
      value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void PigeonFirebaseSettings::set_cache_size_bytes(int64_t value_arg) {
  cache_size_bytes_ = value_arg;
}

bool PigeonFirebaseSettings::ignore_undefined_properties() const {
  return ignore_undefined_properties_;
}

void PigeonFirebaseSettings::set_ignore_undefined_properties(bool value_arg) {
  ignore_undefined_properties_ = value_arg;
}

EncodableList PigeonFirebaseSettings::ToEncodableList() const {
  EncodableList list;
  list.reserve(5);
  list.push_back(persistence_enabled_ ? EncodableValue(*persistence_enabled_)
                                      : EncodableValue());
  list.push_back(host_ ? EncodableValue(*host_) : EncodableValue());
  list.push_back(ssl_enabled_ ? EncodableValue(*ssl_enabled_)
                              : EncodableValue());
  list.push_back(cache_size_bytes_ ? EncodableValue(*cache_size_bytes_)
                                   : EncodableValue());
  list.push_back(EncodableValue(ignore_undefined_properties_));
  return list;
}

PigeonFirebaseSettings PigeonFirebaseSettings::FromEncodableList(
    const EncodableList& list) {
  PigeonFirebaseSettings decoded(std::get<bool>(list[4]));
  auto& encodable_persistence_enabled = list[0];
  if (!encodable_persistence_enabled.IsNull()) {
    decoded.set_persistence_enabled(
        std::get<bool>(encodable_persistence_enabled));
  }
  auto& encodable_host = list[1];
  if (!encodable_host.IsNull()) {
    decoded.set_host(std::get<std::string>(encodable_host));
  }
  auto& encodable_ssl_enabled = list[2];
  if (!encodable_ssl_enabled.IsNull()) {
    decoded.set_ssl_enabled(std::get<bool>(encodable_ssl_enabled));
  }
  auto& encodable_cache_size_bytes = list[3];
  if (!encodable_cache_size_bytes.IsNull()) {
    decoded.set_cache_size_bytes(encodable_cache_size_bytes.LongValue());
  }
  return decoded;
}

// FirestorePigeonFirebaseApp

FirestorePigeonFirebaseApp::FirestorePigeonFirebaseApp(
    const std::string& app_name, const PigeonFirebaseSettings& settings,
    const std::string& database_u_r_l)
    : app_name_(app_name),
      settings_(settings),
      database_u_r_l_(database_u_r_l) {}

const std::string& FirestorePigeonFirebaseApp::app_name() const {
  return app_name_;
}

void FirestorePigeonFirebaseApp::set_app_name(std::string_view value_arg) {
  app_name_ = value_arg;
}

const PigeonFirebaseSettings& FirestorePigeonFirebaseApp::settings() const {
  return settings_;
}

void FirestorePigeonFirebaseApp::set_settings(
    const PigeonFirebaseSettings& value_arg) {
  settings_ = value_arg;
}

const std::string& FirestorePigeonFirebaseApp::database_u_r_l() const {
  return database_u_r_l_;
}

void FirestorePigeonFirebaseApp::set_database_u_r_l(
    std::string_view value_arg) {
  database_u_r_l_ = value_arg;
}

EncodableList FirestorePigeonFirebaseApp::ToEncodableList() const {
  EncodableList list;
  list.reserve(3);
  list.push_back(EncodableValue(app_name_));
  list.push_back(EncodableValue(settings_.ToEncodableList()));
  list.push_back(EncodableValue(database_u_r_l_));
  return list;
}

FirestorePigeonFirebaseApp FirestorePigeonFirebaseApp::FromEncodableList(
    const EncodableList& list) {
  FirestorePigeonFirebaseApp decoded(std::get<std::string>(list[0]),
                                     PigeonFirebaseSettings::FromEncodableList(
                                         std::get<EncodableList>(list[1])),
                                     std::get<std::string>(list[2]));
  return decoded;
}

// PigeonSnapshotMetadata

PigeonSnapshotMetadata::PigeonSnapshotMetadata(bool has_pending_writes,
                                               bool is_from_cache)
    : has_pending_writes_(has_pending_writes), is_from_cache_(is_from_cache) {}

bool PigeonSnapshotMetadata::has_pending_writes() const {
  return has_pending_writes_;
}

void PigeonSnapshotMetadata::set_has_pending_writes(bool value_arg) {
  has_pending_writes_ = value_arg;
}

bool PigeonSnapshotMetadata::is_from_cache() const { return is_from_cache_; }

void PigeonSnapshotMetadata::set_is_from_cache(bool value_arg) {
  is_from_cache_ = value_arg;
}

EncodableList PigeonSnapshotMetadata::ToEncodableList() const {
  EncodableList list;
  list.reserve(2);
  list.push_back(EncodableValue(has_pending_writes_));
  list.push_back(EncodableValue(is_from_cache_));
  return list;
}

PigeonSnapshotMetadata PigeonSnapshotMetadata::FromEncodableList(
    const EncodableList& list) {
  PigeonSnapshotMetadata decoded(std::get<bool>(list[0]),
                                 std::get<bool>(list[1]));
  return decoded;
}

// PigeonDocumentSnapshot

PigeonDocumentSnapshot::PigeonDocumentSnapshot(
    const std::string& path, const PigeonSnapshotMetadata& metadata)
    : path_(path), metadata_(metadata) {}

PigeonDocumentSnapshot::PigeonDocumentSnapshot(
    const std::string& path, const EncodableMap* data,
    const PigeonSnapshotMetadata& metadata)
    : path_(path),
      data_(data ? std::optional<EncodableMap>(*data) : std::nullopt),
      metadata_(metadata) {}

const std::string& PigeonDocumentSnapshot::path() const { return path_; }

void PigeonDocumentSnapshot::set_path(std::string_view value_arg) {
  path_ = value_arg;
}

const EncodableMap* PigeonDocumentSnapshot::data() const {
  return data_ ? &(*data_) : nullptr;
}

void PigeonDocumentSnapshot::set_data(const EncodableMap* value_arg) {
  data_ = value_arg ? std::optional<EncodableMap>(*value_arg) : std::nullopt;
}

void PigeonDocumentSnapshot::set_data(const EncodableMap& value_arg) {
  data_ = value_arg;
}

const PigeonSnapshotMetadata& PigeonDocumentSnapshot::metadata() const {
  return metadata_;
}

void PigeonDocumentSnapshot::set_metadata(
    const PigeonSnapshotMetadata& value_arg) {
  metadata_ = value_arg;
}

EncodableList PigeonDocumentSnapshot::ToEncodableList() const {
  EncodableList list;
  list.reserve(3);
  list.push_back(EncodableValue(path_));
  list.push_back(data_ ? EncodableValue(*data_) : EncodableValue());
  list.push_back(EncodableValue(metadata_.ToEncodableList()));
  return list;
}

PigeonDocumentSnapshot PigeonDocumentSnapshot::FromEncodableList(
    const EncodableList& list) {
  PigeonDocumentSnapshot decoded(std::get<std::string>(list[0]),
                                 PigeonSnapshotMetadata::FromEncodableList(
                                     std::get<EncodableList>(list[2])));
  auto& encodable_data = list[1];
  if (!encodable_data.IsNull()) {
    decoded.set_data(std::get<EncodableMap>(encodable_data));
  }
  return decoded;
}

// PigeonDocumentChange

PigeonDocumentChange::PigeonDocumentChange(
    const DocumentChangeType& type, const PigeonDocumentSnapshot& document,
    int64_t old_index, int64_t new_index)
    : type_(type),
      document_(document),
      old_index_(old_index),
      new_index_(new_index) {}

const DocumentChangeType& PigeonDocumentChange::type() const { return type_; }

void PigeonDocumentChange::set_type(const DocumentChangeType& value_arg) {
  type_ = value_arg;
}

const PigeonDocumentSnapshot& PigeonDocumentChange::document() const {
  return document_;
}

void PigeonDocumentChange::set_document(
    const PigeonDocumentSnapshot& value_arg) {
  document_ = value_arg;
}

int64_t PigeonDocumentChange::old_index() const { return old_index_; }

void PigeonDocumentChange::set_old_index(int64_t value_arg) {
  old_index_ = value_arg;
}

int64_t PigeonDocumentChange::new_index() const { return new_index_; }

void PigeonDocumentChange::set_new_index(int64_t value_arg) {
  new_index_ = value_arg;
}

EncodableList PigeonDocumentChange::ToEncodableList() const {
  EncodableList list;
  list.reserve(4);
  list.push_back(EncodableValue((int)type_));
  list.push_back(EncodableValue(document_.ToEncodableList()));
  list.push_back(EncodableValue(old_index_));
  list.push_back(EncodableValue(new_index_));
  return list;
}

PigeonDocumentChange PigeonDocumentChange::FromEncodableList(
    const EncodableList& list) {
  PigeonDocumentChange decoded((DocumentChangeType)(std::get<int32_t>(list[0])),
                               PigeonDocumentSnapshot::FromEncodableList(
                                   std::get<EncodableList>(list[1])),
                               list[2].LongValue(), list[3].LongValue());
  return decoded;
}

// PigeonQuerySnapshot

PigeonQuerySnapshot::PigeonQuerySnapshot(const EncodableList& documents,
                                         const EncodableList& document_changes,
                                         const PigeonSnapshotMetadata& metadata)
    : documents_(documents),
      document_changes_(document_changes),
      metadata_(metadata) {}

const EncodableList& PigeonQuerySnapshot::documents() const {
  return documents_;
}

void PigeonQuerySnapshot::set_documents(const EncodableList& value_arg) {
  documents_ = value_arg;
}

const EncodableList& PigeonQuerySnapshot::document_changes() const {
  return document_changes_;
}

void PigeonQuerySnapshot::set_document_changes(const EncodableList& value_arg) {
  document_changes_ = value_arg;
}

const PigeonSnapshotMetadata& PigeonQuerySnapshot::metadata() const {
  return metadata_;
}

void PigeonQuerySnapshot::set_metadata(
    const PigeonSnapshotMetadata& value_arg) {
  metadata_ = value_arg;
}

EncodableList PigeonQuerySnapshot::ToEncodableList() const {
  EncodableList list;
  list.reserve(3);
  list.push_back(EncodableValue(documents_));
  list.push_back(EncodableValue(document_changes_));
  list.push_back(EncodableValue(metadata_.ToEncodableList()));
  return list;
}

PigeonQuerySnapshot PigeonQuerySnapshot::FromEncodableList(
    const EncodableList& list) {
  PigeonQuerySnapshot decoded(std::get<EncodableList>(list[0]),
                              std::get<EncodableList>(list[1]),
                              PigeonSnapshotMetadata::FromEncodableList(
                                  std::get<EncodableList>(list[2])));
  return decoded;
}

// PigeonGetOptions

PigeonGetOptions::PigeonGetOptions(
    const Source& source,
    const ServerTimestampBehavior& server_timestamp_behavior)
    : source_(source), server_timestamp_behavior_(server_timestamp_behavior) {}

const Source& PigeonGetOptions::source() const { return source_; }

void PigeonGetOptions::set_source(const Source& value_arg) {
  source_ = value_arg;
}

const ServerTimestampBehavior& PigeonGetOptions::server_timestamp_behavior()
    const {
  return server_timestamp_behavior_;
}

void PigeonGetOptions::set_server_timestamp_behavior(
    const ServerTimestampBehavior& value_arg) {
  server_timestamp_behavior_ = value_arg;
}

EncodableList PigeonGetOptions::ToEncodableList() const {
  EncodableList list;
  list.reserve(2);
  list.push_back(EncodableValue((int)source_));
  list.push_back(EncodableValue((int)server_timestamp_behavior_));
  return list;
}

PigeonGetOptions PigeonGetOptions::FromEncodableList(
    const EncodableList& list) {
  PigeonGetOptions decoded(
      (Source)(std::get<int32_t>(list[0])),
      (ServerTimestampBehavior)(std::get<int32_t>(list[1])));
  return decoded;
}

// PigeonDocumentOption

PigeonDocumentOption::PigeonDocumentOption() {}

PigeonDocumentOption::PigeonDocumentOption(const bool* merge,
                                           const EncodableList* merge_fields)
    : merge_(merge ? std::optional<bool>(*merge) : std::nullopt),
      merge_fields_(merge_fields ? std::optional<EncodableList>(*merge_fields)
                                 : std::nullopt) {}

const bool* PigeonDocumentOption::merge() const {
  return merge_ ? &(*merge_) : nullptr;
}

void PigeonDocumentOption::set_merge(const bool* value_arg) {
  merge_ = value_arg ? std::optional<bool>(*value_arg) : std::nullopt;
}

void PigeonDocumentOption::set_merge(bool value_arg) { merge_ = value_arg; }

const EncodableList* PigeonDocumentOption::merge_fields() const {
  return merge_fields_ ? &(*merge_fields_) : nullptr;
}

void PigeonDocumentOption::set_merge_fields(const EncodableList* value_arg) {
  merge_fields_ =
      value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonDocumentOption::set_merge_fields(const EncodableList& value_arg) {
  merge_fields_ = value_arg;
}

EncodableList PigeonDocumentOption::ToEncodableList() const {
  EncodableList list;
  list.reserve(2);
  list.push_back(merge_ ? EncodableValue(*merge_) : EncodableValue());
  list.push_back(merge_fields_ ? EncodableValue(*merge_fields_)
                               : EncodableValue());
  return list;
}

PigeonDocumentOption PigeonDocumentOption::FromEncodableList(
    const EncodableList& list) {
  PigeonDocumentOption decoded;
  auto& encodable_merge = list[0];
  if (!encodable_merge.IsNull()) {
    decoded.set_merge(std::get<bool>(encodable_merge));
  }
  auto& encodable_merge_fields = list[1];
  if (!encodable_merge_fields.IsNull()) {
    decoded.set_merge_fields(std::get<EncodableList>(encodable_merge_fields));
  }
  return decoded;
}

// PigeonTransactionCommand

PigeonTransactionCommand::PigeonTransactionCommand(
    const PigeonTransactionType& type, const std::string& path)
    : type_(type), path_(path) {}

PigeonTransactionCommand::PigeonTransactionCommand(
    const PigeonTransactionType& type, const std::string& path,
    const EncodableMap* data, const PigeonDocumentOption* option)
    : type_(type),
      path_(path),
      data_(data ? std::optional<EncodableMap>(*data) : std::nullopt),
      option_(option ? std::optional<PigeonDocumentOption>(*option)
                     : std::nullopt) {}

const PigeonTransactionType& PigeonTransactionCommand::type() const {
  return type_;
}

void PigeonTransactionCommand::set_type(
    const PigeonTransactionType& value_arg) {
  type_ = value_arg;
}

const std::string& PigeonTransactionCommand::path() const { return path_; }

void PigeonTransactionCommand::set_path(std::string_view value_arg) {
  path_ = value_arg;
}

const EncodableMap* PigeonTransactionCommand::data() const {
  return data_ ? &(*data_) : nullptr;
}

void PigeonTransactionCommand::set_data(const EncodableMap* value_arg) {
  data_ = value_arg ? std::optional<EncodableMap>(*value_arg) : std::nullopt;
}

void PigeonTransactionCommand::set_data(const EncodableMap& value_arg) {
  data_ = value_arg;
}

const PigeonDocumentOption* PigeonTransactionCommand::option() const {
  return option_ ? &(*option_) : nullptr;
}

void PigeonTransactionCommand::set_option(
    const PigeonDocumentOption* value_arg) {
  option_ = value_arg ? std::optional<PigeonDocumentOption>(*value_arg)
                      : std::nullopt;
}

void PigeonTransactionCommand::set_option(
    const PigeonDocumentOption& value_arg) {
  option_ = value_arg;
}

EncodableList PigeonTransactionCommand::ToEncodableList() const {
  EncodableList list;
  list.reserve(4);
  list.push_back(EncodableValue((int)type_));
  list.push_back(EncodableValue(path_));
  list.push_back(data_ ? EncodableValue(*data_) : EncodableValue());
  list.push_back(option_ ? EncodableValue(option_->ToEncodableList())
                         : EncodableValue());
  return list;
}

PigeonTransactionCommand PigeonTransactionCommand::FromEncodableList(
    const EncodableList& list) {
  PigeonTransactionCommand decoded(
      (PigeonTransactionType)(std::get<int32_t>(list[0])),
      std::get<std::string>(list[1]));
  auto& encodable_data = list[2];
  if (!encodable_data.IsNull()) {
    decoded.set_data(std::get<EncodableMap>(encodable_data));
  }
  auto& encodable_option = list[3];
  if (!encodable_option.IsNull()) {
    decoded.set_option(PigeonDocumentOption::FromEncodableList(
        std::get<EncodableList>(encodable_option)));
  }
  return decoded;
}

// DocumentReferenceRequest

DocumentReferenceRequest::DocumentReferenceRequest(const std::string& path)
    : path_(path) {}

DocumentReferenceRequest::DocumentReferenceRequest(
    const std::string& path, const EncodableMap* data,
    const PigeonDocumentOption* option, const Source* source,
    const ServerTimestampBehavior* server_timestamp_behavior)
    : path_(path),
      data_(data ? std::optional<EncodableMap>(*data) : std::nullopt),
      option_(option ? std::optional<PigeonDocumentOption>(*option)
                     : std::nullopt),
      source_(source ? std::optional<Source>(*source) : std::nullopt),
      server_timestamp_behavior_(server_timestamp_behavior
                                     ? std::optional<ServerTimestampBehavior>(
                                           *server_timestamp_behavior)
                                     : std::nullopt) {}

const std::string& DocumentReferenceRequest::path() const { return path_; }

void DocumentReferenceRequest::set_path(std::string_view value_arg) {
  path_ = value_arg;
}

const EncodableMap* DocumentReferenceRequest::data() const {
  return data_ ? &(*data_) : nullptr;
}

void DocumentReferenceRequest::set_data(const EncodableMap* value_arg) {
  data_ = value_arg ? std::optional<EncodableMap>(*value_arg) : std::nullopt;
}

void DocumentReferenceRequest::set_data(const EncodableMap& value_arg) {
  data_ = value_arg;
}

const PigeonDocumentOption* DocumentReferenceRequest::option() const {
  return option_ ? &(*option_) : nullptr;
}

void DocumentReferenceRequest::set_option(
    const PigeonDocumentOption* value_arg) {
  option_ = value_arg ? std::optional<PigeonDocumentOption>(*value_arg)
                      : std::nullopt;
}

void DocumentReferenceRequest::set_option(
    const PigeonDocumentOption& value_arg) {
  option_ = value_arg;
}

const Source* DocumentReferenceRequest::source() const {
  return source_ ? &(*source_) : nullptr;
}

void DocumentReferenceRequest::set_source(const Source* value_arg) {
  source_ = value_arg ? std::optional<Source>(*value_arg) : std::nullopt;
}

void DocumentReferenceRequest::set_source(const Source& value_arg) {
  source_ = value_arg;
}

const ServerTimestampBehavior*
DocumentReferenceRequest::server_timestamp_behavior() const {
  return server_timestamp_behavior_ ? &(*server_timestamp_behavior_) : nullptr;
}

void DocumentReferenceRequest::set_server_timestamp_behavior(
    const ServerTimestampBehavior* value_arg) {
  server_timestamp_behavior_ =
      value_arg ? std::optional<ServerTimestampBehavior>(*value_arg)
                : std::nullopt;
}

void DocumentReferenceRequest::set_server_timestamp_behavior(
    const ServerTimestampBehavior& value_arg) {
  server_timestamp_behavior_ = value_arg;
}

EncodableList DocumentReferenceRequest::ToEncodableList() const {
  EncodableList list;
  list.reserve(5);
  list.push_back(EncodableValue(path_));
  list.push_back(data_ ? EncodableValue(*data_) : EncodableValue());
  list.push_back(option_ ? EncodableValue(option_->ToEncodableList())
                         : EncodableValue());
  list.push_back(source_ ? EncodableValue((int)(*source_)) : EncodableValue());
  list.push_back(server_timestamp_behavior_
                     ? EncodableValue((int)(*server_timestamp_behavior_))
                     : EncodableValue());
  return list;
}

DocumentReferenceRequest DocumentReferenceRequest::FromEncodableList(
    const EncodableList& list) {
  DocumentReferenceRequest decoded(std::get<std::string>(list[0]));
  auto& encodable_data = list[1];
  if (!encodable_data.IsNull()) {
    decoded.set_data(std::get<EncodableMap>(encodable_data));
  }
  auto& encodable_option = list[2];
  if (!encodable_option.IsNull()) {
    decoded.set_option(PigeonDocumentOption::FromEncodableList(
        std::get<EncodableList>(encodable_option)));
  }
  auto& encodable_source = list[3];
  if (!encodable_source.IsNull()) {
    decoded.set_source((Source)(std::get<int32_t>(encodable_source)));
  }
  auto& encodable_server_timestamp_behavior = list[4];
  if (!encodable_server_timestamp_behavior.IsNull()) {
    decoded.set_server_timestamp_behavior(
        (ServerTimestampBehavior)(std::get<int32_t>(
            encodable_server_timestamp_behavior)));
  }
  return decoded;
}

// PigeonQueryParameters

PigeonQueryParameters::PigeonQueryParameters() {}

PigeonQueryParameters::PigeonQueryParameters(
    const EncodableList* where, const EncodableList* order_by,
    const int64_t* limit, const int64_t* limit_to_last,
    const EncodableList* start_at, const EncodableList* start_after,
    const EncodableList* end_at, const EncodableList* end_before,
    const EncodableMap* filters)
    : where_(where ? std::optional<EncodableList>(*where) : std::nullopt),
      order_by_(order_by ? std::optional<EncodableList>(*order_by)
                         : std::nullopt),
      limit_(limit ? std::optional<int64_t>(*limit) : std::nullopt),
      limit_to_last_(limit_to_last ? std::optional<int64_t>(*limit_to_last)
                                   : std::nullopt),
      start_at_(start_at ? std::optional<EncodableList>(*start_at)
                         : std::nullopt),
      start_after_(start_after ? std::optional<EncodableList>(*start_after)
                               : std::nullopt),
      end_at_(end_at ? std::optional<EncodableList>(*end_at) : std::nullopt),
      end_before_(end_before ? std::optional<EncodableList>(*end_before)
                             : std::nullopt),
      filters_(filters ? std::optional<EncodableMap>(*filters) : std::nullopt) {
}

const EncodableList* PigeonQueryParameters::where() const {
  return where_ ? &(*where_) : nullptr;
}

void PigeonQueryParameters::set_where(const EncodableList* value_arg) {
  where_ = value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_where(const EncodableList& value_arg) {
  where_ = value_arg;
}

const EncodableList* PigeonQueryParameters::order_by() const {
  return order_by_ ? &(*order_by_) : nullptr;
}

void PigeonQueryParameters::set_order_by(const EncodableList* value_arg) {
  order_by_ =
      value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_order_by(const EncodableList& value_arg) {
  order_by_ = value_arg;
}

const int64_t* PigeonQueryParameters::limit() const {
  return limit_ ? &(*limit_) : nullptr;
}

void PigeonQueryParameters::set_limit(const int64_t* value_arg) {
  limit_ = value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_limit(int64_t value_arg) { limit_ = value_arg; }

const int64_t* PigeonQueryParameters::limit_to_last() const {
  return limit_to_last_ ? &(*limit_to_last_) : nullptr;
}

void PigeonQueryParameters::set_limit_to_last(const int64_t* value_arg) {
  limit_to_last_ =
      value_arg ? std::optional<int64_t>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_limit_to_last(int64_t value_arg) {
  limit_to_last_ = value_arg;
}

const EncodableList* PigeonQueryParameters::start_at() const {
  return start_at_ ? &(*start_at_) : nullptr;
}

void PigeonQueryParameters::set_start_at(const EncodableList* value_arg) {
  start_at_ =
      value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_start_at(const EncodableList& value_arg) {
  start_at_ = value_arg;
}

const EncodableList* PigeonQueryParameters::start_after() const {
  return start_after_ ? &(*start_after_) : nullptr;
}

void PigeonQueryParameters::set_start_after(const EncodableList* value_arg) {
  start_after_ =
      value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_start_after(const EncodableList& value_arg) {
  start_after_ = value_arg;
}

const EncodableList* PigeonQueryParameters::end_at() const {
  return end_at_ ? &(*end_at_) : nullptr;
}

void PigeonQueryParameters::set_end_at(const EncodableList* value_arg) {
  end_at_ = value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_end_at(const EncodableList& value_arg) {
  end_at_ = value_arg;
}

const EncodableList* PigeonQueryParameters::end_before() const {
  return end_before_ ? &(*end_before_) : nullptr;
}

void PigeonQueryParameters::set_end_before(const EncodableList* value_arg) {
  end_before_ =
      value_arg ? std::optional<EncodableList>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_end_before(const EncodableList& value_arg) {
  end_before_ = value_arg;
}

const EncodableMap* PigeonQueryParameters::filters() const {
  return filters_ ? &(*filters_) : nullptr;
}

void PigeonQueryParameters::set_filters(const EncodableMap* value_arg) {
  filters_ = value_arg ? std::optional<EncodableMap>(*value_arg) : std::nullopt;
}

void PigeonQueryParameters::set_filters(const EncodableMap& value_arg) {
  filters_ = value_arg;
}

EncodableList PigeonQueryParameters::ToEncodableList() const {
  EncodableList list;
  list.reserve(9);
  list.push_back(where_ ? EncodableValue(*where_) : EncodableValue());
  list.push_back(order_by_ ? EncodableValue(*order_by_) : EncodableValue());
  list.push_back(limit_ ? EncodableValue(*limit_) : EncodableValue());
  list.push_back(limit_to_last_ ? EncodableValue(*limit_to_last_)
                                : EncodableValue());
  list.push_back(start_at_ ? EncodableValue(*start_at_) : EncodableValue());
  list.push_back(start_after_ ? EncodableValue(*start_after_)
                              : EncodableValue());
  list.push_back(end_at_ ? EncodableValue(*end_at_) : EncodableValue());
  list.push_back(end_before_ ? EncodableValue(*end_before_) : EncodableValue());
  list.push_back(filters_ ? EncodableValue(*filters_) : EncodableValue());
  return list;
}

PigeonQueryParameters PigeonQueryParameters::FromEncodableList(
    const EncodableList& list) {
  PigeonQueryParameters decoded;
  auto& encodable_where = list[0];
  if (!encodable_where.IsNull()) {
    decoded.set_where(std::get<EncodableList>(encodable_where));
  }
  auto& encodable_order_by = list[1];
  if (!encodable_order_by.IsNull()) {
    decoded.set_order_by(std::get<EncodableList>(encodable_order_by));
  }
  auto& encodable_limit = list[2];
  if (!encodable_limit.IsNull()) {
    decoded.set_limit(encodable_limit.LongValue());
  }
  auto& encodable_limit_to_last = list[3];
  if (!encodable_limit_to_last.IsNull()) {
    decoded.set_limit_to_last(encodable_limit_to_last.LongValue());
  }
  auto& encodable_start_at = list[4];
  if (!encodable_start_at.IsNull()) {
    decoded.set_start_at(std::get<EncodableList>(encodable_start_at));
  }
  auto& encodable_start_after = list[5];
  if (!encodable_start_after.IsNull()) {
    decoded.set_start_after(std::get<EncodableList>(encodable_start_after));
  }
  auto& encodable_end_at = list[6];
  if (!encodable_end_at.IsNull()) {
    decoded.set_end_at(std::get<EncodableList>(encodable_end_at));
  }
  auto& encodable_end_before = list[7];
  if (!encodable_end_before.IsNull()) {
    decoded.set_end_before(std::get<EncodableList>(encodable_end_before));
  }
  auto& encodable_filters = list[8];
  if (!encodable_filters.IsNull()) {
    decoded.set_filters(std::get<EncodableMap>(encodable_filters));
  }
  return decoded;
}

// AggregateQuery

AggregateQuery::AggregateQuery(const AggregateType& type) : type_(type) {}

AggregateQuery::AggregateQuery(const AggregateType& type,
                               const std::string* field)
    : type_(type),
      field_(field ? std::optional<std::string>(*field) : std::nullopt) {}

const AggregateType& AggregateQuery::type() const { return type_; }

void AggregateQuery::set_type(const AggregateType& value_arg) {
  type_ = value_arg;
}

const std::string* AggregateQuery::field() const {
  return field_ ? &(*field_) : nullptr;
}

void AggregateQuery::set_field(const std::string_view* value_arg) {
  field_ = value_arg ? std::optional<std::string>(*value_arg) : std::nullopt;
}

void AggregateQuery::set_field(std::string_view value_arg) {
  field_ = value_arg;
}

EncodableList AggregateQuery::ToEncodableList() const {
  EncodableList list;
  list.reserve(2);
  list.push_back(EncodableValue((int)type_));
  list.push_back(field_ ? EncodableValue(*field_) : EncodableValue());
  return list;
}

AggregateQuery AggregateQuery::FromEncodableList(const EncodableList& list) {
  AggregateQuery decoded((AggregateType)(std::get<int32_t>(list[0])));
  auto& encodable_field = list[1];
  if (!encodable_field.IsNull()) {
    decoded.set_field(std::get<std::string>(encodable_field));
  }
  return decoded;
}

// AggregateQueryResponse

AggregateQueryResponse::AggregateQueryResponse(const AggregateType& type)
    : type_(type) {}

AggregateQueryResponse::AggregateQueryResponse(const AggregateType& type,
                                               const std::string* field,
                                               const double* value)
    : type_(type),
      field_(field ? std::optional<std::string>(*field) : std::nullopt),
      value_(value ? std::optional<double>(*value) : std::nullopt) {}

const AggregateType& AggregateQueryResponse::type() const { return type_; }

void AggregateQueryResponse::set_type(const AggregateType& value_arg) {
  type_ = value_arg;
}

const std::string* AggregateQueryResponse::field() const {
  return field_ ? &(*field_) : nullptr;
}

void AggregateQueryResponse::set_field(const std::string_view* value_arg) {
  field_ = value_arg ? std::optional<std::string>(*value_arg) : std::nullopt;
}

void AggregateQueryResponse::set_field(std::string_view value_arg) {
  field_ = value_arg;
}

const double* AggregateQueryResponse::value() const {
  return value_ ? &(*value_) : nullptr;
}

void AggregateQueryResponse::set_value(const double* value_arg) {
  value_ = value_arg ? std::optional<double>(*value_arg) : std::nullopt;
}

void AggregateQueryResponse::set_value(double value_arg) { value_ = value_arg; }

EncodableList AggregateQueryResponse::ToEncodableList() const {
  EncodableList list;
  list.reserve(3);
  list.push_back(EncodableValue((int)type_));
  list.push_back(field_ ? EncodableValue(*field_) : EncodableValue());
  list.push_back(value_ ? EncodableValue(*value_) : EncodableValue());
  return list;
}

AggregateQueryResponse AggregateQueryResponse::FromEncodableList(
    const EncodableList& list) {
  AggregateQueryResponse decoded((AggregateType)(std::get<int32_t>(list[0])));
  auto& encodable_field = list[1];
  if (!encodable_field.IsNull()) {
    decoded.set_field(std::get<std::string>(encodable_field));
  }
  auto& encodable_value = list[2];
  if (!encodable_value.IsNull()) {
    decoded.set_value(std::get<double>(encodable_value));
  }
  return decoded;
}

FirebaseFirestoreHostApiCodecSerializer::
    FirebaseFirestoreHostApiCodecSerializer() {}

EncodableValue FirebaseFirestoreHostApiCodecSerializer::ReadValueOfType(
    uint8_t type, flutter::ByteStreamReader* stream) const {
  switch (type) {
    case 128:
      return CustomEncodableValue(AggregateQuery::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 129:
      return CustomEncodableValue(AggregateQueryResponse::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 130:
      return CustomEncodableValue(DocumentReferenceRequest::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 131:
      return CustomEncodableValue(FirestorePigeonFirebaseApp::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 132:
      return CustomEncodableValue(PigeonDocumentChange::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 133:
      return CustomEncodableValue(PigeonDocumentOption::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 134:
      return CustomEncodableValue(PigeonDocumentSnapshot::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 135:
      return CustomEncodableValue(PigeonFirebaseSettings::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 136:
      return CustomEncodableValue(PigeonGetOptions::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 137:
      return CustomEncodableValue(PigeonQueryParameters::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 138:
      return CustomEncodableValue(PigeonQuerySnapshot::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 139:
      return CustomEncodableValue(PigeonSnapshotMetadata::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    case 140:
      return CustomEncodableValue(PigeonTransactionCommand::FromEncodableList(
          std::get<EncodableList>(ReadValue(stream))));
    default:
      return cloud_firestore_windows::FirestoreCodec::ReadValueOfType(type,
                                                                      stream);
  }
}

void FirebaseFirestoreHostApiCodecSerializer::WriteValue(
    const EncodableValue& value, flutter::ByteStreamWriter* stream) const {
  if (const CustomEncodableValue* custom_value =
          std::get_if<CustomEncodableValue>(&value)) {
    if (custom_value->type() == typeid(AggregateQuery)) {
      stream->WriteByte(128);
      WriteValue(
          EncodableValue(
              std::any_cast<AggregateQuery>(*custom_value).ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(AggregateQueryResponse)) {
      stream->WriteByte(129);
      WriteValue(
          EncodableValue(std::any_cast<AggregateQueryResponse>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(DocumentReferenceRequest)) {
      stream->WriteByte(130);
      WriteValue(
          EncodableValue(std::any_cast<DocumentReferenceRequest>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(FirestorePigeonFirebaseApp)) {
      stream->WriteByte(131);
      WriteValue(EncodableValue(
                     std::any_cast<FirestorePigeonFirebaseApp>(*custom_value)
                         .ToEncodableList()),
                 stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonDocumentChange)) {
      stream->WriteByte(132);
      WriteValue(
          EncodableValue(std::any_cast<PigeonDocumentChange>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonDocumentOption)) {
      stream->WriteByte(133);
      WriteValue(
          EncodableValue(std::any_cast<PigeonDocumentOption>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonDocumentSnapshot)) {
      stream->WriteByte(134);
      WriteValue(
          EncodableValue(std::any_cast<PigeonDocumentSnapshot>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonFirebaseSettings)) {
      stream->WriteByte(135);
      WriteValue(
          EncodableValue(std::any_cast<PigeonFirebaseSettings>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonGetOptions)) {
      stream->WriteByte(136);
      WriteValue(
          EncodableValue(
              std::any_cast<PigeonGetOptions>(*custom_value).ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonQueryParameters)) {
      stream->WriteByte(137);
      WriteValue(
          EncodableValue(std::any_cast<PigeonQueryParameters>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonQuerySnapshot)) {
      stream->WriteByte(138);
      WriteValue(
          EncodableValue(std::any_cast<PigeonQuerySnapshot>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonSnapshotMetadata)) {
      stream->WriteByte(139);
      WriteValue(
          EncodableValue(std::any_cast<PigeonSnapshotMetadata>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
    if (custom_value->type() == typeid(PigeonTransactionCommand)) {
      stream->WriteByte(140);
      WriteValue(
          EncodableValue(std::any_cast<PigeonTransactionCommand>(*custom_value)
                             .ToEncodableList()),
          stream);
      return;
    }
  }
  cloud_firestore_windows::FirestoreCodec::WriteValue(value, stream);
}

/// The codec used by FirebaseFirestoreHostApi.
const flutter::StandardMessageCodec& FirebaseFirestoreHostApi::GetCodec() {
  return flutter::StandardMessageCodec::GetInstance(
      &FirebaseFirestoreHostApiCodecSerializer::GetInstance());
}

// Sets up an instance of `FirebaseFirestoreHostApi` to handle messages through
// the `binary_messenger`.
void FirebaseFirestoreHostApi::SetUp(flutter::BinaryMessenger* binary_messenger,
                                     FirebaseFirestoreHostApi* api) {
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.loadBundle",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_bundle_arg = args.at(1);
              if (encodable_bundle_arg.IsNull()) {
                reply(WrapError("bundle_arg unexpectedly null."));
                return;
              }
              const auto& bundle_arg =
                  std::get<std::vector<uint8_t>>(encodable_bundle_arg);
              api->LoadBundle(
                  app_arg, bundle_arg, [reply](ErrorOr<std::string>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        EncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.namedQueryGet",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_name_arg = args.at(1);
              if (encodable_name_arg.IsNull()) {
                reply(WrapError("name_arg unexpectedly null."));
                return;
              }
              const auto& name_arg = std::get<std::string>(encodable_name_arg);
              const auto& encodable_options_arg = args.at(2);
              if (encodable_options_arg.IsNull()) {
                reply(WrapError("options_arg unexpectedly null."));
                return;
              }
              const auto& options_arg = std::any_cast<const PigeonGetOptions&>(
                  std::get<CustomEncodableValue>(encodable_options_arg));
              api->NamedQueryGet(
                  app_arg, name_arg, options_arg,
                  [reply](ErrorOr<PigeonQuerySnapshot>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        CustomEncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.clearPersistence",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              api->ClearPersistence(
                  app_arg, [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.disableNetwork",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              api->DisableNetwork(
                  app_arg, [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.enableNetwork",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              api->EnableNetwork(app_arg,
                                 [reply](std::optional<FlutterError>&& output) {
                                   if (output.has_value()) {
                                     reply(WrapError(output.value()));
                                     return;
                                   }
                                   EncodableList wrapped;
                                   wrapped.push_back(EncodableValue());
                                   reply(EncodableValue(std::move(wrapped)));
                                 });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.terminate",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              api->Terminate(app_arg,
                             [reply](std::optional<FlutterError>&& output) {
                               if (output.has_value()) {
                                 reply(WrapError(output.value()));
                                 return;
                               }
                               EncodableList wrapped;
                               wrapped.push_back(EncodableValue());
                               reply(EncodableValue(std::move(wrapped)));
                             });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.waitForPendingWrites",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              api->WaitForPendingWrites(
                  app_arg, [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.setIndexConfiguration",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_index_configuration_arg = args.at(1);
              if (encodable_index_configuration_arg.IsNull()) {
                reply(WrapError("index_configuration_arg unexpectedly null."));
                return;
              }
              const auto& index_configuration_arg =
                  std::get<std::string>(encodable_index_configuration_arg);
              api->SetIndexConfiguration(
                  app_arg, index_configuration_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.setLoggingEnabled",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_logging_enabled_arg = args.at(0);
              if (encodable_logging_enabled_arg.IsNull()) {
                reply(WrapError("logging_enabled_arg unexpectedly null."));
                return;
              }
              const auto& logging_enabled_arg =
                  std::get<bool>(encodable_logging_enabled_arg);
              api->SetLoggingEnabled(
                  logging_enabled_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.snapshotsInSyncSetup",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              api->SnapshotsInSyncSetup(
                  app_arg, [reply](ErrorOr<std::string>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        EncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.transactionCreate",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_timeout_arg = args.at(1);
              if (encodable_timeout_arg.IsNull()) {
                reply(WrapError("timeout_arg unexpectedly null."));
                return;
              }
              const int64_t timeout_arg = encodable_timeout_arg.LongValue();
              const auto& encodable_max_attempts_arg = args.at(2);
              if (encodable_max_attempts_arg.IsNull()) {
                reply(WrapError("max_attempts_arg unexpectedly null."));
                return;
              }
              const int64_t max_attempts_arg =
                  encodable_max_attempts_arg.LongValue();
              api->TransactionCreate(
                  app_arg, timeout_arg, max_attempts_arg,
                  [reply](ErrorOr<std::string>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        EncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.transactionStoreResult",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_transaction_id_arg = args.at(0);
              if (encodable_transaction_id_arg.IsNull()) {
                reply(WrapError("transaction_id_arg unexpectedly null."));
                return;
              }
              const auto& transaction_id_arg =
                  std::get<std::string>(encodable_transaction_id_arg);
              const auto& encodable_result_type_arg = args.at(1);
              if (encodable_result_type_arg.IsNull()) {
                reply(WrapError("result_type_arg unexpectedly null."));
                return;
              }
              const PigeonTransactionResult& result_type_arg =
                  (PigeonTransactionResult)
                      encodable_result_type_arg.LongValue();
              const auto& encodable_commands_arg = args.at(2);
              const auto* commands_arg =
                  std::get_if<EncodableList>(&encodable_commands_arg);
              api->TransactionStoreResult(
                  transaction_id_arg, result_type_arg, commands_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.transactionGet",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_transaction_id_arg = args.at(1);
              if (encodable_transaction_id_arg.IsNull()) {
                reply(WrapError("transaction_id_arg unexpectedly null."));
                return;
              }
              const auto& transaction_id_arg =
                  std::get<std::string>(encodable_transaction_id_arg);
              const auto& encodable_path_arg = args.at(2);
              if (encodable_path_arg.IsNull()) {
                reply(WrapError("path_arg unexpectedly null."));
                return;
              }
              const auto& path_arg = std::get<std::string>(encodable_path_arg);
              api->TransactionGet(
                  app_arg, transaction_id_arg, path_arg,
                  [reply](ErrorOr<PigeonDocumentSnapshot>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        CustomEncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.documentReferenceSet",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_request_arg = args.at(1);
              if (encodable_request_arg.IsNull()) {
                reply(WrapError("request_arg unexpectedly null."));
                return;
              }
              const auto& request_arg =
                  std::any_cast<const DocumentReferenceRequest&>(
                      std::get<CustomEncodableValue>(encodable_request_arg));
              api->DocumentReferenceSet(
                  app_arg, request_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.documentReferenceUpdate",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_request_arg = args.at(1);
              if (encodable_request_arg.IsNull()) {
                reply(WrapError("request_arg unexpectedly null."));
                return;
              }
              const auto& request_arg =
                  std::any_cast<const DocumentReferenceRequest&>(
                      std::get<CustomEncodableValue>(encodable_request_arg));
              api->DocumentReferenceUpdate(
                  app_arg, request_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.documentReferenceGet",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_request_arg = args.at(1);
              if (encodable_request_arg.IsNull()) {
                reply(WrapError("request_arg unexpectedly null."));
                return;
              }
              const auto& request_arg =
                  std::any_cast<const DocumentReferenceRequest&>(
                      std::get<CustomEncodableValue>(encodable_request_arg));
              api->DocumentReferenceGet(
                  app_arg, request_arg,
                  [reply](ErrorOr<PigeonDocumentSnapshot>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        CustomEncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.documentReferenceDelete",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_request_arg = args.at(1);
              if (encodable_request_arg.IsNull()) {
                reply(WrapError("request_arg unexpectedly null."));
                return;
              }
              const auto& request_arg =
                  std::any_cast<const DocumentReferenceRequest&>(
                      std::get<CustomEncodableValue>(encodable_request_arg));
              api->DocumentReferenceDelete(
                  app_arg, request_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.queryGet",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_path_arg = args.at(1);
              if (encodable_path_arg.IsNull()) {
                reply(WrapError("path_arg unexpectedly null."));
                return;
              }
              const auto& path_arg = std::get<std::string>(encodable_path_arg);
              const auto& encodable_is_collection_group_arg = args.at(2);
              if (encodable_is_collection_group_arg.IsNull()) {
                reply(WrapError("is_collection_group_arg unexpectedly null."));
                return;
              }
              const auto& is_collection_group_arg =
                  std::get<bool>(encodable_is_collection_group_arg);
              const auto& encodable_parameters_arg = args.at(3);
              if (encodable_parameters_arg.IsNull()) {
                reply(WrapError("parameters_arg unexpectedly null."));
                return;
              }
              const auto& parameters_arg =
                  std::any_cast<const PigeonQueryParameters&>(
                      std::get<CustomEncodableValue>(encodable_parameters_arg));
              const auto& encodable_options_arg = args.at(4);
              if (encodable_options_arg.IsNull()) {
                reply(WrapError("options_arg unexpectedly null."));
                return;
              }
              const auto& options_arg = std::any_cast<const PigeonGetOptions&>(
                  std::get<CustomEncodableValue>(encodable_options_arg));
              api->QueryGet(
                  app_arg, path_arg, is_collection_group_arg, parameters_arg,
                  options_arg, [reply](ErrorOr<PigeonQuerySnapshot>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        CustomEncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.aggregateQuery",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_path_arg = args.at(1);
              if (encodable_path_arg.IsNull()) {
                reply(WrapError("path_arg unexpectedly null."));
                return;
              }
              const auto& path_arg = std::get<std::string>(encodable_path_arg);
              const auto& encodable_parameters_arg = args.at(2);
              if (encodable_parameters_arg.IsNull()) {
                reply(WrapError("parameters_arg unexpectedly null."));
                return;
              }
              const auto& parameters_arg =
                  std::any_cast<const PigeonQueryParameters&>(
                      std::get<CustomEncodableValue>(encodable_parameters_arg));
              const auto& encodable_source_arg = args.at(3);
              if (encodable_source_arg.IsNull()) {
                reply(WrapError("source_arg unexpectedly null."));
                return;
              }
              const AggregateSource& source_arg =
                  (AggregateSource)encodable_source_arg.LongValue();
              const auto& encodable_queries_arg = args.at(4);
              if (encodable_queries_arg.IsNull()) {
                reply(WrapError("queries_arg unexpectedly null."));
                return;
              }
              const auto& queries_arg =
                  std::get<EncodableList>(encodable_queries_arg);
              const auto& encodable_is_collection_group_arg = args.at(5);
              if (encodable_is_collection_group_arg.IsNull()) {
                reply(WrapError("is_collection_group_arg unexpectedly null."));
                return;
              }
              const auto& is_collection_group_arg =
                  std::get<bool>(encodable_is_collection_group_arg);
              api->AggregateQuery(app_arg, path_arg, parameters_arg, source_arg,
                                  queries_arg, is_collection_group_arg,
                                  [reply](ErrorOr<EncodableList>&& output) {
                                    if (output.has_error()) {
                                      reply(WrapError(output.error()));
                                      return;
                                    }
                                    EncodableList wrapped;
                                    wrapped.push_back(EncodableValue(
                                        std::move(output).TakeValue()));
                                    reply(EncodableValue(std::move(wrapped)));
                                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.writeBatchCommit",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_writes_arg = args.at(1);
              if (encodable_writes_arg.IsNull()) {
                reply(WrapError("writes_arg unexpectedly null."));
                return;
              }
              const auto& writes_arg =
                  std::get<EncodableList>(encodable_writes_arg);
              api->WriteBatchCommit(
                  app_arg, writes_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.querySnapshot",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_path_arg = args.at(1);
              if (encodable_path_arg.IsNull()) {
                reply(WrapError("path_arg unexpectedly null."));
                return;
              }
              const auto& path_arg = std::get<std::string>(encodable_path_arg);
              const auto& encodable_is_collection_group_arg = args.at(2);
              if (encodable_is_collection_group_arg.IsNull()) {
                reply(WrapError("is_collection_group_arg unexpectedly null."));
                return;
              }
              const auto& is_collection_group_arg =
                  std::get<bool>(encodable_is_collection_group_arg);
              const auto& encodable_parameters_arg = args.at(3);
              if (encodable_parameters_arg.IsNull()) {
                reply(WrapError("parameters_arg unexpectedly null."));
                return;
              }
              const auto& parameters_arg =
                  std::any_cast<const PigeonQueryParameters&>(
                      std::get<CustomEncodableValue>(encodable_parameters_arg));
              const auto& encodable_options_arg = args.at(4);
              if (encodable_options_arg.IsNull()) {
                reply(WrapError("options_arg unexpectedly null."));
                return;
              }
              const auto& options_arg = std::any_cast<const PigeonGetOptions&>(
                  std::get<CustomEncodableValue>(encodable_options_arg));
              const auto& encodable_include_metadata_changes_arg = args.at(5);
              if (encodable_include_metadata_changes_arg.IsNull()) {
                reply(WrapError(
                    "include_metadata_changes_arg unexpectedly null."));
                return;
              }
              const auto& include_metadata_changes_arg =
                  std::get<bool>(encodable_include_metadata_changes_arg);
              const auto& encodable_source_arg = args.at(6);
              if (encodable_source_arg.IsNull()) {
                reply(WrapError("source_arg unexpectedly null."));
                return;
              }
              const ListenSource& source_arg =
                  (ListenSource)encodable_source_arg.LongValue();
              api->QuerySnapshot(
                  app_arg, path_arg, is_collection_group_arg, parameters_arg,
                  options_arg, include_metadata_changes_arg, source_arg,
                  [reply](ErrorOr<std::string>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        EncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.documentReferenceSnapshot",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_parameters_arg = args.at(1);
              if (encodable_parameters_arg.IsNull()) {
                reply(WrapError("parameters_arg unexpectedly null."));
                return;
              }
              const auto& parameters_arg =
                  std::any_cast<const DocumentReferenceRequest&>(
                      std::get<CustomEncodableValue>(encodable_parameters_arg));
              const auto& encodable_include_metadata_changes_arg = args.at(2);
              if (encodable_include_metadata_changes_arg.IsNull()) {
                reply(WrapError(
                    "include_metadata_changes_arg unexpectedly null."));
                return;
              }
              const auto& include_metadata_changes_arg =
                  std::get<bool>(encodable_include_metadata_changes_arg);
              const auto& encodable_source_arg = args.at(3);
              if (encodable_source_arg.IsNull()) {
                reply(WrapError("source_arg unexpectedly null."));
                return;
              }
              const ListenSource& source_arg =
                  (ListenSource)encodable_source_arg.LongValue();
              api->DocumentReferenceSnapshot(
                  app_arg, parameters_arg, include_metadata_changes_arg,
                  source_arg, [reply](ErrorOr<std::string>&& output) {
                    if (output.has_error()) {
                      reply(WrapError(output.error()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(
                        EncodableValue(std::move(output).TakeValue()));
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
  {
    auto channel = std::make_unique<BasicMessageChannel<>>(
        binary_messenger,
        "dev.flutter.pigeon.cloud_firestore_platform_interface."
        "FirebaseFirestoreHostApi.persistenceCacheIndexManagerRequest",
        &GetCodec());
    if (api != nullptr) {
      channel->SetMessageHandler(
          [api](const EncodableValue& message,
                const flutter::MessageReply<EncodableValue>& reply) {
            try {
              const auto& args = std::get<EncodableList>(message);
              const auto& encodable_app_arg = args.at(0);
              if (encodable_app_arg.IsNull()) {
                reply(WrapError("app_arg unexpectedly null."));
                return;
              }
              const auto& app_arg =
                  std::any_cast<const FirestorePigeonFirebaseApp&>(
                      std::get<CustomEncodableValue>(encodable_app_arg));
              const auto& encodable_request_arg = args.at(1);
              if (encodable_request_arg.IsNull()) {
                reply(WrapError("request_arg unexpectedly null."));
                return;
              }
              const PersistenceCacheIndexManagerRequestEnum& request_arg =
                  (PersistenceCacheIndexManagerRequestEnum)
                      encodable_request_arg.LongValue();
              api->PersistenceCacheIndexManagerRequest(
                  app_arg, request_arg,
                  [reply](std::optional<FlutterError>&& output) {
                    if (output.has_value()) {
                      reply(WrapError(output.value()));
                      return;
                    }
                    EncodableList wrapped;
                    wrapped.push_back(EncodableValue());
                    reply(EncodableValue(std::move(wrapped)));
                  });
            } catch (const std::exception& exception) {
              reply(WrapError(exception.what()));
            }
          });
    } else {
      channel->SetMessageHandler(nullptr);
    }
  }
}

EncodableValue FirebaseFirestoreHostApi::WrapError(
    std::string_view error_message) {
  return EncodableValue(
      EncodableList{EncodableValue(std::string(error_message)),
                    EncodableValue("Error"), EncodableValue()});
}

EncodableValue FirebaseFirestoreHostApi::WrapError(const FlutterError& error) {
  return EncodableValue(EncodableList{EncodableValue(error.code()),
                                      EncodableValue(error.message()),
                                      error.details()});
}

}  // namespace cloud_firestore_windows
